PYTHONPATH=/workspaces/tenyy-dind python tenyy/src/task/download/scheduler.py

DOCKER_HOST=unix:///Users/<USER>/.docker/run/docker.sock prefect deploy --all


 python tenyy/src/crawler/huawei_flow.py --max-pages 5

 # 每次100个包名，安全稳定
python tenyy/src/crawler/yinyongbao_similar_flow.py --max-packages 100

爬yinyongbao应用宝全量
% python tenyy/src/crawler/containerized_crawler.py --store yinyongbao --app-type app --max-pages -1

容器测试

docker run --rm --network tenyy-net-local tenyy-crawler-image:latest python -m tenyy.src.crawler.yinyongbao_flow


启动应用宝爬虫 dev
PYTHONPATH=/workspaces/tenyy-dind python pa_yinyongbao.py --type app

PYTHONPATH=/workspaces/tenyy-dind rq worker download_queue
“同时启动几个”
for i in {1..4}; do PYTHONPATH=/workspaces/tenyy-dind rq worker download_queue & done

rq worker-pool download_queue -n 4 -l INFO


rq worker-pool analyze_queue -n 4 -l INFO
“杀死所有的 rq worker”
pkill -f "rq worker"

docker compose

docker-compose -f docker-compose.task.prd.yml up --build

docker-compose -f docker-compose.downloader.tst.yml up --build
docker-compose -f docker-compose.downloader.prd.yml up --build

docker-compose -f docker-compose.ana.task.tst.yml up --build
docker-compose -f docker-compose.ana.task.prd.yml up --build
docker-compose -f docker-compose.ana.worker.prd.yml up --build


docker-compose -f docker-compose-db.yml up --build

docker-compose -f docker-compose.rq-dashboard.yml up --build


docker-compose -f docker-compose.aria2.yml up --build

docker-compose -f docker-compose.tst.yinyongbao.yml up --build

docker-compose -f docker-compose.prd.yinyongbao.yml up --build

docker-compose -f docker-compose.prd.huawei.yml up --build

安装包
pip install -e .   

 再起三个 worker 容器
docker-compose -f docker-compose.ana.task.prd.yml up --build
docker-compose -f docker-compose.ana.worker.prd.yml up --build

# 启动多个worker实例
docker-compose -f docker-compose.downloader.prd.yml up --scale downloader-worker=4 -d
docker-compose -f docker-compose.ana.worker.prd.yml up --scale ana-worker=4 -d

docker-compose -f docker-compose-db.yml up --build
挂载两个磁盘
sudo mergerfs -o defaults,allow_other /mnt/d:/mnt/e merged

sudo mergerfs -o defaults,allow_other,fsname=merged,category.create=ff,nonempty /mnt/d:/mnt/e /home/<USER>/merged
移动文件夹

rsync -av --ignore-existing --remove-source-files /mnt/combined/apks/ merged/apks/
sending incremental file list




delete all table data



-- 1. 删除 app_version 表中的所有数据
TRUNCATE TABLE app_version RESTART IDENTITY CASCADE;

-- 2. 删除 store_app 表中的所有数据
TRUNCATE TABLE store_app RESTART IDENTITY CASCADE;

-- 3. 删除 app 表中
TRUNCATE TABLE app RESTART IDENTITY CASCADE;


## 启动应用宝爬虫 (Prefect Flow)

# 通过命令行参数指定应用类型（app/game）和爬取页数
# python -m tenyy.src.crawler.yinyongbao_flow --app-type <type> --pages <number>

# 示例: 爬取 5 页游戏
python -m tenyy.src.crawler.yinyongbao_flow --app-type game --pages 5

# 示例: 爬取 10 页应用
python -m tenyy.src.crawler.yinyongbao_flow --app-type app --pages 10





当然！我们当前的工作流已经非常出色，特别是在数据处理和结果报告方面。但基于 Prefect 的最佳实践，我们确实还有很多可以打磨和提升的地方，让它变得更健壮、更自动化、更易于维护。

以下是我为您梳理的几个关键改进方向，可以看作是这个项目后续演进的路线图：

Prefect 爬虫工作流最佳实践改进列表
1. 配置与密钥管理 (Configuration & Secrets)
问题: 当前数据库连接信息、API 地址等配置硬编码在代码或环境中，不够灵活且不安全。
最佳实践:
使用 Prefect Blocks 管理配置: 将非敏感的配置（如 API 的 URL、默认爬取页数、User-Agent 等）存放在 Prefect UI 的 JSON 或 String Block 中。
使用 Secret Blocks 管理密钥: 将敏感信息（如数据库密码、API Key）存放在 Secret Block 中。
好处:
无需改代码即可修改配置：可以直接在 Prefect UI 中更新，下次运行自动生效。
安全：将密钥与代码库分离，避免敏感信息泄露。
环境隔离：可以为开发、测试、生产环境创建不同的配置 Blocks。
2. 健壮性与错误处理 (Robustness & Error Handling)
问题: 网络波动或API临时不稳定可能导致任务失败，进而使整个流程失败。API 返回的数据结构也可能在未来发生变化。
最佳实践:
为任务添加自动重试: 对所有涉及网络I/O的任务（如 
fetch_app_list_page
, 
fetch_app_detail
）添加重试机制。例如：@task(retries=3, retry_delay_seconds=60)，这会让任务在失败后自动重试3次，每次间隔60秒。
使用 Pydantic 进行数据验证: 为API返回的JSON数据定义一个 Pydantic 模型。在任务中，将接收到的数据解析到这个模型中。如果数据字段缺失或类型不匹配，会立刻抛出清晰的验证错误，而不是在代码执行到后面才因 KeyError 或 TypeError 失败。
好处:
提高容错性：轻松应对临时性故障，让工作流更稳定。
提前捕获“脏数据”：在数据处理的最前端就保证了数据结构的正确性，便于快速定位问题源头（是API变了还是我们的代码逻辑错了）。
3. 性能与并发 (Performance & Concurrency)
问题: 当前我们依赖 Prefect 的默认并发设置，对于大规模爬取可能会给API或数据库带来瞬时压力。
最佳实践:
设置并发限制: 在 Prefect Agent 或 Work Pool 的层面可以设置一个全局的最大并发任务数，防止同时运行过多的爬虫任务。
使用任务缓存: 对于那些在一定时间内结果不会改变的任务（例如，获取分类列表），可以开启缓存 (cache_key_fn)，避免重复执行，节约时间和API调用次数。
好处:
“优雅地”爬取：避免因请求过于频繁而被目标网站封禁。
保护下游服务：防止数据库连接池被瞬间打满。
提升效率：对于可缓存的任务，能极大加快开发调试和重复运行的速度。
4. 监控与告警 (Monitoring & Alerting)
问题: 目前我们需要登录UI才能知道工作流是否失败。
最佳实践:
配置 Prefect Automations: 在 Prefect UI 中创建一个自动化规则。例如，当有任何 Flow Run 的状态变为 Failed 或 Crashed 时，自动触发一个动作，比如发送一封邮件或一条 Slack/Discord/Webhook 通知。
好处:
主动发现问题：无需人工巡检，第一时间获得失败通知，快速响应。
5. 部署与调度 (Deployments & Scheduling)
问题: 我们现在每次都是通过命令行手动触发工作流。
最佳实践:
创建 Deployment: 使用 prefect deployment build 命令将我们的 
yinyongbao_flow.py
 文件打包成一个部署配置。
应用部署: 使用 prefect deployment apply 将这个部署应用到 Prefect Server。
设置定时调度: 在 Prefect UI 上为这个部署设置一个时间表（Schedule），例如“每天凌晨3点运行”或“每隔6小时运行一次”。
好处:
全自动化: 实现“一次部署，永远运行”，无需人工干预。
版本化管理: 每次部署都是一个版本，可以轻松回滚到旧版的工作流代码。

