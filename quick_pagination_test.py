#!/usr/bin/env python3
"""
快速测试应用宝爬虫翻页功能
"""
import sys
import os
sys.path.append('/Users/<USER>/dev/tenyy-dind')

from tenyy.src.crawler.yinyongbao_crawler import YinyongbaoCrawler
import time

def quick_test_pagination(app_type='app', max_test_pages=100):
    """
    快速测试翻页功能
    """
    print(f"🚀 快速测试应用宝 {app_type} 翻页功能")
    print(f"📄 最大测试页数: {max_test_pages}")
    print("-" * 50)
    
    try:
        # 初始化爬虫
        crawler = YinyongbaoCrawler()
        crawler.set_app_type(app_type)
        
        # 模拟分类信息
        category = {'app_type': app_type, 'name': f'{app_type}_test'}
        
        page_num = 1
        total_apps = 0
        consecutive_empty_pages = 0
        
        while page_num <= max_test_pages:
            try:
                # 爬取当前页
                app_list, has_next_page = crawler.scrape_list_page(category, page_num)
                
                apps_count = len(app_list)
                total_apps += apps_count
                
                # 每10页输出一次进度
                if page_num % 10 == 0 or apps_count == 0 or not has_next_page:
                    print(f"📄 第 {page_num} 页: {apps_count} 个应用 (累计: {total_apps}) {'→' if has_next_page else '🏁'}")
                
                # 如果没有应用，计数连续空页
                if apps_count == 0:
                    consecutive_empty_pages += 1
                    if consecutive_empty_pages >= 3:  # 连续3页空页就停止
                        print(f"🛑 连续 {consecutive_empty_pages} 页空页，停止测试")
                        break
                else:
                    consecutive_empty_pages = 0
                    
                # 如果明确标记没有下一页，停止测试
                if not has_next_page:
                    print(f"🛑 第 {page_num} 页标记为最后一页，停止翻页")
                    break
                
                # 短暂延迟
                time.sleep(0.5)
                page_num += 1
                
            except Exception as e:
                print(f"❌ 第 {page_num} 页出错: {e}")
                break
        
        # 输出测试结果
        print("\n" + "=" * 50)
        print("📊 快速测试结果")
        print("=" * 50)
        print(f"🎯 应用类型: {app_type}")
        print(f"📄 测试到第: {page_num} 页")
        print(f"📱 总应用数: {total_apps}")
        print(f"📊 平均每页: {total_apps/page_num:.1f} 个应用")
        
        return page_num, total_apps
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return 0, 0

def main():
    """主函数"""
    print("⚡ 应用宝翻页快速测试")
    print("=" * 50)
    
    # 只测试应用类型，看看能翻多少页
    app_pages, app_total = quick_test_pagination('app', max_test_pages=200)
    
    print(f"\n🎉 测试完成!")
    print(f"📱 应用类型最多能翻到第 {app_pages} 页，共 {app_total} 个应用")

if __name__ == "__main__":
    main()
