#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分页式运行应用宝推荐应用爬虫
每次处理指定数量的包名，支持断点续爬
"""

import sys
import argparse
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from tenyy.src.crawler.yinyongbao_similar_crawler import YinyongbaoSimilarCrawler
from tenyy.src.crawler.yinyongbao_similar_flow import yinyongbao_similar_crawler_flow


def run_paginated_crawler(page_size: int = 100, start_page: int = 1, max_pages: int = None):
    """
    分页运行推荐应用爬虫
    
    Args:
        page_size: 每页处理的包名数量
        start_page: 开始页码（从1开始）
        max_pages: 最大页数，None表示处理到最后一页
    """
    print(f"🚀 启动分页式应用宝推荐应用爬虫")
    print(f"📄 每页大小: {page_size}")
    print(f"🎯 开始页码: {start_page}")
    print(f"📊 最大页数: {max_pages or '无限制'}")
    print("=" * 60)
    
    # 初始化爬虫获取总数
    crawler = YinyongbaoSimilarCrawler()
    total_packages = crawler.get_total_package_count()
    total_pages = (total_packages + page_size - 1) // page_size  # 向上取整
    
    print(f"📦 数据库中总包名数: {total_packages}")
    print(f"📄 总页数: {total_pages}")
    
    if max_pages:
        end_page = min(start_page + max_pages - 1, total_pages)
    else:
        end_page = total_pages
    
    print(f"🎯 将处理页码: {start_page} - {end_page}")
    print("=" * 60)
    
    # 分页处理
    total_success = 0
    total_failed = 0
    
    for current_page in range(start_page, end_page + 1):
        offset = (current_page - 1) * page_size
        
        print(f"\n📄 处理第 {current_page}/{total_pages} 页")
        print(f"   偏移量: {offset}")
        print(f"   页大小: {page_size}")
        
        # 获取当前页的包名
        package_names = crawler.get_package_names_from_db(limit=page_size, offset=offset)
        
        if not package_names:
            print(f"⚠️  第 {current_page} 页没有数据，跳过")
            continue
        
        print(f"   实际获取: {len(package_names)} 个包名")
        print(f"   包名示例: {package_names[:3]}...")
        
        try:
            # 创建临时爬虫实例处理当前页
            page_crawler = YinyongbaoSimilarCrawler()
            
            # 手动设置包名列表
            categories = []
            for pkg_name in package_names:
                categories.append({
                    'name': f'Similar to {pkg_name}',
                    'package_name': pkg_name,
                    'type': 'similar'
                })
            
            # 处理当前页的所有包名
            page_success = 0
            page_failed = 0
            
            for i, category in enumerate(categories, 1):
                print(f"   📦 处理包名 {i}/{len(categories)}: {category['package_name']}")
                
                try:
                    # 爬取推荐应用
                    app_list, _ = page_crawler.scrape_list_page(category, page_num=1)
                    
                    if app_list:
                        print(f"      找到 {len(app_list)} 个推荐应用")
                        
                        # 处理每个推荐应用
                        for app in app_list:
                            try:
                                # 爬取详情页
                                detail_data = page_crawler.scrape_detail_page(app.get('pkgName'))
                                
                                # 合并数据
                                if detail_data:
                                    merged_data = {**app, **detail_data}
                                else:
                                    merged_data = app
                                
                                # 标准化和保存
                                standardized_data = page_crawler.process_and_standardize(merged_data)
                                
                                if page_crawler.validate_data(standardized_data):
                                    from tenyy.src.common.data_writer import save_app_data
                                    save_stats = save_app_data(standardized_data, store_type='yinyongbao')
                                    
                                    if save_stats:
                                        page_success += 1
                                    # else: 版本已存在，也算成功
                                else:
                                    page_failed += 1
                                    
                            except Exception as e:
                                page_failed += 1
                                print(f"      ❌ 处理应用失败: {e}")
                    else:
                        print(f"      ⚠️  没有找到推荐应用")
                        
                except Exception as e:
                    page_failed += 1
                    print(f"   ❌ 处理包名失败: {e}")
            
            total_success += page_success
            total_failed += page_failed
            
            print(f"   ✅ 第 {current_page} 页完成: 成功 {page_success}, 失败 {page_failed}")
            
        except Exception as e:
            print(f"   ❌ 第 {current_page} 页处理失败: {e}")
            continue
        
        # 页间休息
        if current_page < end_page:
            print(f"   😴 休息 2 秒...")
            time.sleep(2)
    
    # 最终统计
    total_processed = total_success + total_failed
    success_rate = (total_success / total_processed * 100) if total_processed > 0 else 0
    
    print("\n" + "=" * 60)
    print("🎉 分页爬取完成!")
    print(f"📊 最终统计:")
    print(f"   - 处理页数: {end_page - start_page + 1}")
    print(f"   - 总应用数: {total_processed}")
    print(f"   - 成功: {total_success}")
    print(f"   - 失败: {total_failed}")
    print(f"   - 成功率: {success_rate:.1f}%")


def main():
    """命令行入口"""
    parser = argparse.ArgumentParser(description='分页式应用宝推荐应用爬虫')
    parser.add_argument('--page-size', type=int, default=100, 
                       help='每页处理的包名数量 (默认: 100)')
    parser.add_argument('--start-page', type=int, default=1,
                       help='开始页码，从1开始 (默认: 1)')
    parser.add_argument('--max-pages', type=int,
                       help='最大处理页数，不指定则处理到最后一页')
    
    args = parser.parse_args()
    
    # 运行分页爬虫
    run_paginated_crawler(
        page_size=args.page_size,
        start_page=args.start_page,
        max_pages=args.max_pages
    )


if __name__ == "__main__":
    main()
