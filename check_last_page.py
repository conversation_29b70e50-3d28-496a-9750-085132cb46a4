#!/usr/bin/env python3
"""
检查最后几页的API响应
"""
import sys
import os
import json
sys.path.append('/Users/<USER>/dev/tenyy-dind')

from tenyy.src.crawler.yinyongbao_crawler import YinyongbaoCrawler
import time

def check_pages_around(app_type='app', start_page=140, end_page=150):
    """
    检查指定范围内的页面
    """
    print(f"🔍 检查应用宝 {app_type} 第 {start_page}-{end_page} 页的详细情况")
    print("-" * 60)
    
    try:
        # 初始化爬虫
        crawler = YinyongbaoCrawler()
        crawler.set_app_type(app_type)
        
        # 模拟分类信息
        category = {'app_type': app_type, 'name': f'{app_type}_test'}
        
        for page_num in range(start_page, end_page + 1):
            print(f"\n📄 检查第 {page_num} 页...")
            
            try:
                # 构建请求数据
                payload = crawler._build_request_payload(app_type, page_num)
                
                # 发送请求
                response = crawler.http_client.post(
                    crawler.api_url,
                    json_data=payload
                )
                
                if not response:
                    print(f"   ❌ 请求失败")
                    continue
                
                json_response = response.json()
                
                # 解析应用列表
                app_list = crawler._parse_app_list(json_response)
                
                # 检查翻页逻辑
                has_next_page = crawler._check_has_next_page(json_response, len(app_list))
                
                print(f"   📱 应用数量: {len(app_list)}")
                print(f"   ➡️  有下一页: {has_next_page}")
                
                # 检查API响应中的关键信息
                data = json_response.get('data', {})
                components = data.get('components', [])
                
                print(f"   🔧 组件数量: {len(components)}")
                
                # 查找分页相关信息
                pagination_info = []
                for i, component in enumerate(components):
                    component_data = component.get('data', {})
                    for key in ['hasMore', 'hasNextPage', 'isEnd', 'totalCount', 'pageSize', 'currentPage']:
                        if key in component_data:
                            pagination_info.append(f"{key}: {component_data[key]}")
                
                if pagination_info:
                    print(f"   📊 分页信息: {', '.join(pagination_info)}")
                else:
                    print(f"   📊 分页信息: 未找到明确的分页字段")
                
                # 如果没有应用，显示更多调试信息
                if len(app_list) == 0:
                    print(f"   🔍 空页面调试信息:")
                    print(f"      - 响应状态码: {response.status_code}")
                    print(f"      - 响应大小: {len(response.text)} 字符")
                    print(f"      - 数据组件: {len(components)} 个")
                    
                    # 保存响应到文件以便分析
                    with open(f'page_{page_num}_response.json', 'w', encoding='utf-8') as f:
                        json.dump(json_response, f, ensure_ascii=False, indent=2)
                    print(f"      - 响应已保存到: page_{page_num}_response.json")
                
                time.sleep(1)  # 避免请求过快
                
            except Exception as e:
                print(f"   ❌ 第 {page_num} 页出错: {e}")
        
    except Exception as e:
        print(f"❌ 检查过程中出现错误: {e}")

def main():
    """主函数"""
    print("🔍 应用宝最后几页详细检查")
    print("=" * 60)
    
    # 检查第140-150页
    check_pages_around('app', 140, 150)

if __name__ == "__main__":
    main()
