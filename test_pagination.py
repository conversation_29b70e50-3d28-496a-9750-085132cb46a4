#!/usr/bin/env python3
"""
测试应用宝爬虫翻页功能
"""
import sys
import os
sys.path.append('/Users/<USER>/dev/tenyy-dind')

from tenyy.src.crawler.yinyongbao_crawler import YinyongbaoCrawler
import time

def test_pagination(app_type='app', max_test_pages=50):
    """
    测试翻页功能
    
    Args:
        app_type: 应用类型 ('app' 或 'game')
        max_test_pages: 最大测试页数（防止无限循环）
    """
    print(f"🚀 开始测试应用宝 {app_type} 翻页功能")
    print(f"📄 最大测试页数: {max_test_pages}")
    print("-" * 50)
    
    try:
        # 初始化爬虫
        crawler = YinyongbaoCrawler()
        crawler.set_app_type(app_type)
        
        # 模拟分类信息
        category = {'app_type': app_type, 'name': f'{app_type}_test'}
        
        page_num = 1
        total_apps = 0
        page_results = []
        
        while page_num <= max_test_pages:
            print(f"\n📄 测试第 {page_num} 页...")
            
            try:
                # 爬取当前页
                app_list, has_next_page = crawler.scrape_list_page(category, page_num)
                
                apps_count = len(app_list)
                total_apps += apps_count
                
                page_info = {
                    'page': page_num,
                    'apps_count': apps_count,
                    'has_next_page': has_next_page,
                    'total_apps_so_far': total_apps
                }
                page_results.append(page_info)
                
                print(f"   ✅ 第 {page_num} 页: {apps_count} 个应用")
                print(f"   📊 累计应用数: {total_apps}")
                print(f"   ➡️  有下一页: {has_next_page}")
                
                # 如果没有应用或没有下一页，停止测试
                if apps_count == 0:
                    print(f"   🛑 第 {page_num} 页没有应用，停止翻页")
                    break
                    
                if not has_next_page:
                    print(f"   🛑 第 {page_num} 页标记为最后一页，停止翻页")
                    break
                
                # 页面间延迟
                time.sleep(2)
                page_num += 1
                
            except Exception as e:
                print(f"   ❌ 第 {page_num} 页出错: {e}")
                break
        
        # 输出测试结果
        print("\n" + "=" * 60)
        print("📊 翻页测试结果汇总")
        print("=" * 60)
        print(f"🎯 应用类型: {app_type}")
        print(f"📄 总页数: {len(page_results)}")
        print(f"📱 总应用数: {total_apps}")
        
        if page_results:
            last_page = page_results[-1]
            print(f"🏁 最后一页: 第 {last_page['page']} 页，{last_page['apps_count']} 个应用")
        
        print("\n📋 每页详情:")
        for result in page_results:
            status = "✅" if result['apps_count'] > 0 else "❌"
            next_indicator = "→" if result['has_next_page'] else "🏁"
            print(f"   {status} 第 {result['page']:2d} 页: {result['apps_count']:2d} 个应用 {next_indicator}")
        
        return page_results
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return []

def main():
    """主函数"""
    print("🧪 应用宝翻页测试工具")
    print("=" * 60)
    
    # 测试应用类型
    print("\n1️⃣ 测试应用类型 (app)")
    app_results = test_pagination('app', max_test_pages=20)
    
    print("\n" + "="*60)
    
    # 测试游戏类型
    print("\n2️⃣ 测试游戏类型 (game)")
    game_results = test_pagination('game', max_test_pages=20)
    
    # 总结
    print("\n" + "="*60)
    print("🎉 测试完成总结")
    print("="*60)
    
    if app_results:
        app_total = sum(r['apps_count'] for r in app_results)
        print(f"📱 应用类型: {len(app_results)} 页，共 {app_total} 个应用")
    
    if game_results:
        game_total = sum(r['apps_count'] for r in game_results)
        print(f"🎮 游戏类型: {len(game_results)} 页，共 {game_total} 个游戏")

if __name__ == "__main__":
    main()
