#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
BaseFlow - 通用爬虫流程

提供标准化的Prefect流程模板，支持：
- 并发处理 (.map)
- 子流结构
- 标准化产物生成
- 错误处理和重试
"""

import time
import random
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
import threading
from prefect import flow, task, get_run_logger
from prefect.futures import as_completed

# 移除中间 artifacts 导入，只保留最终结果 artifacts
# 并发控制配置 - 直接在代码中定义以避免连接池满的问题
CONCURRENT_DETAIL_REQUESTS = 3  # 降低并发数
BATCH_SIZE = 3  # 小批次处理
BATCH_DELAY = 2  # 增加延迟
from tenyy.src.common.data_writer import save_app_data
from .base_crawler import BaseCrawler
from .flow_utils import create_standard_artifacts, StandardCrawlReport


@task(name="Process and Save App", retries=2, retry_delay_seconds=5)
def process_and_save_app_task(raw_data: Dict[str, Any], crawler: BaseCrawler, **kwargs) -> Dict[str, Any]:
    """
    处理和保存单个应用的通用任务
    
    Args:
        raw_data: 原始应用数据
        crawler: 爬虫实例
        **kwargs: 额外参数
        
    Returns:
        处理结果
    """
    logger = get_run_logger()
    
    # 基础结果结构
    base_result = {
        'store_id': raw_data.get('detailId') or raw_data.get('appId') or raw_data.get('store_id', 'unknown'),
        'name': raw_data.get('name') or raw_data.get('appName', 'unknown'),
        'status': 'failure',
        'error': None,
        'is_new_app': False,
        'is_new_version': False
    }
    
    try:
        # 1. 数据标准化
        standardized_data = crawler.process_and_standardize(raw_data, **kwargs)
        
        # 2. 数据验证
        if not crawler.validate_data(standardized_data):
            base_result['error'] = 'Data validation failed'
            return base_result
        
        pkg_name = standardized_data.get('pkg_name')
        if not pkg_name:
            base_result['error'] = 'Missing package name'
            return base_result
        
        # 3. 保存到数据库
        save_stats = save_app_data(standardized_data, store_type=crawler.store_type)
        
        if save_stats is None:  # 版本已存在
            base_result.update({
                'status': 'skipped',
                'error': 'Version already exists'
            })
        else:
            base_result.update({
                'status': 'success',
                'error': None,
                'is_new_app': save_stats.get('new_app', False),
                'is_new_version': save_stats.get('new_version', False)
            })
        
        return base_result
        
    except Exception as e:
        logger.error(f"Failed to process app {base_result['store_id']}: {e}", exc_info=True)
        base_result['error'] = str(e)
        return base_result


@task(name="Scrape List Page", retries=3, retry_delay_seconds=10)
def scrape_list_page_task(crawler: BaseCrawler, category: Dict[str, Any], page_num: int) -> Tuple[List[Dict[str, Any]], bool]:
    """
    爬取列表页的通用任务
    
    Args:
        crawler: 爬虫实例
        category: 分类信息
        page_num: 页码
        
    Returns:
        (应用列表, 是否有下一页)
    """
    logger = get_run_logger()
    logger.info(f"Scraping page {page_num} for category {category.get('name', 'unknown')}")
    
    try:
        return crawler.scrape_list_page(category, page_num)
    except Exception as e:
        logger.error(f"Failed to scrape page {page_num}: {e}")
        return [], False


@task(name="Scrape Detail Page", retries=2, retry_delay_seconds=5)
def scrape_detail_page_task(crawler: BaseCrawler, app_id: str) -> Optional[Dict[str, Any]]:
    """
    爬取详情页的通用任务
    
    Args:
        crawler: 爬虫实例
        app_id: 应用ID
        
    Returns:
        应用详情数据
    """
    logger = get_run_logger()
    
    try:
        return crawler.scrape_detail_page(app_id)
    except Exception as e:
        logger.error(f"Failed to scrape detail for {app_id}: {e}")
        return None


def scrape_detail_worker(args):
    """
    进程池工作函数 - 爬取单个详情页

    Args:
        args: (crawler_config, app_id) 元组

    Returns:
        (app_id, detail_data) 元组
    """
    try:
        # 重新创建爬虫实例（因为进程隔离）
        from .base_crawler import BaseCrawler
        crawler_config, app_id = args

        # 根据store_type创建对应的爬虫实例
        store_type = crawler_config.get('store_type')
        if store_type == 'huawei':
            from ..huawei_crawler import HuaweiCrawler
            crawler = HuaweiCrawler()
        elif store_type == 'yinyongbao':
            from ..yinyongbao_crawler import YinyongbaoCrawler
            crawler = YinyongbaoCrawler()
        else:
            raise ValueError(f"Unsupported store type: {store_type}")

        # 设置爬虫配置（仅应用宝需要）
        if 'app_type' in crawler_config and hasattr(crawler, 'set_app_type'):
            crawler.set_app_type(crawler_config['app_type'])

        # 爬取详情页
        detail_data = crawler.scrape_detail_page(app_id)
        return (app_id, detail_data)

    except Exception as e:
        print(f"Worker failed to scrape detail page for {app_id}: {e}")
        return (app_id, None)


def process_details_with_thread_pool(app_list: List[Dict[str, Any]],
                                   crawler: BaseCrawler,
                                   **kwargs) -> List[Dict[str, Any]]:
    """
    使用线程池并发处理详情页 - 更适合I/O密集型爬虫任务

    Args:
        app_list: 应用列表
        crawler: 爬虫实例
        **kwargs: 额外参数

    Returns:
        处理结果列表
    """
    logger = get_run_logger()

    # 获取配置
    max_workers = crawler.get_config_value('crawl_config.internal_pool_workers', 10)  # 线程池默认10个线程
    store_type = crawler.store_type
    app_type = getattr(crawler, 'app_type', None)

    # 准备爬虫配置（用于进程间传递）
    crawler_config = {
        'store_type': store_type,
        'app_type': app_type
    }

    # 准备任务参数
    tasks = []
    for app in app_list:
        app_id = app.get('detailId') or app.get('appId') or app.get('pkg_name') or app.get('store_id')
        if app_id:
            tasks.append((crawler_config, str(app_id)))

    logger.info(f"Starting thread pool with {max_workers} workers for {len(tasks)} tasks")

    # 不在应用级别创建 artifacts，避免产生大量 markdown artifacts
    # 只记录统计信息，让主进程负责进度显示
    total_tasks = len(tasks)
    completed_tasks = 0
    successful_tasks = 0
    failed_tasks = 0

    # 使用线程池并发处理 - 更适合I/O密集型爬虫任务，容器兼容性更好
    results = []
    logger.info(f"Creating ThreadPoolExecutor with {max_workers} workers...")

    # 使用 with 语句管理线程池，线程池在容器中没有进程池的兼容性问题
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        logger.info("ThreadPoolExecutor created successfully")

        # 提交所有任务
        future_to_app = {}
        logger.info(f"Submitting {len(tasks)} tasks to thread pool...")

        for i, (task_args, app) in enumerate(zip(tasks, app_list)):
            future = executor.submit(scrape_detail_worker, task_args)
            future_to_app[future] = app
            if i % 10 == 0:  # 每10个任务记录一次
                logger.info(f"Submitted task {i+1}/{len(tasks)}")

        logger.info(f"All {len(tasks)} tasks submitted, collecting results...")

        # 收集结果
        completed_count = 0
        for future in future_to_app:
            app = future_to_app[future]
            completed_count += 1
            logger.info(f"Processing result {completed_count}/{len(future_to_app)} for app: {app.get('name', 'Unknown')[:30]}")

            try:
                logger.info(f"Getting result from future (timeout=60s)...")
                app_id, detail_data = future.result(timeout=60)  # 60秒超时
                logger.info(f"Got result for app_id: {app_id}, has_detail_data: {detail_data is not None}")

                if detail_data:
                    # 智能合并数据：详情页数据优先，但不覆盖列表页的有效数据
                    merged_data = app.copy()

                    # 特别保存列表页的关键字段，避免被详情页的空值或错误值覆盖
                    list_page_download_url = app.get('download_url')
                    list_page_name = app.get('name')

                    for key, value in detail_data.items():
                        # 只有当详情页数据非空时才覆盖列表页数据
                        if value is not None and value != '' and value != []:
                            merged_data[key] = value
                        # 如果列表页没有这个字段，则使用详情页数据（即使为空）
                        elif key not in merged_data:
                            merged_data[key] = value

                    # 特殊处理：如果详情页覆盖了列表页的有效download_url，恢复它
                    if (list_page_download_url and
                        list_page_download_url.startswith(('http://', 'https://')) and
                        (not merged_data.get('download_url') or merged_data.get('download_url') == '')):
                        merged_data['download_url'] = list_page_download_url
                        merged_data['_list_page_download_url'] = list_page_download_url  # 备份

                    # 特殊处理：对于华为爬虫，优先使用列表页的应用名称
                    store_type = getattr(crawler, 'store_type', 'default')
                    if (store_type == 'huawei' and list_page_name and
                        list_page_name not in ['应用介绍', 'App Introduction', '详情', 'Details']):
                        merged_data['name'] = list_page_name
                        merged_data['_list_page_name'] = list_page_name  # 备份
                        logger.info(f"Using list page name for Huawei app: {list_page_name}")
                    result = process_and_save_app_task(merged_data, crawler, **kwargs)
                    if result:
                        results.append(result)
                        successful_tasks += 1
                        logger.info(f"✓ Processed {app.get('name', 'Unknown')} with detail data")
                    else:
                        failed_tasks += 1
                else:
                    # 详情页失败，使用原始数据
                    result = process_and_save_app_task(app, crawler, **kwargs)
                    if result:
                        results.append(result)
                        successful_tasks += 1
                        logger.warning(f"⚠ Processed {app.get('name', 'Unknown')} without detail data")
                    else:
                        failed_tasks += 1

            except Exception as e:
                logger.error(f"Failed to process {app.get('name', 'Unknown')}: {e}")
                # 尝试保存原始数据
                try:
                    result = process_and_save_app_task(app, crawler, **kwargs)
                    if result:
                        results.append(result)
                        successful_tasks += 1
                    else:
                        failed_tasks += 1
                except Exception as save_error:
                    logger.error(f"Failed to save app data: {save_error}")
                    failed_tasks += 1

            # 更新统计（不创建 artifacts）
            completed_tasks += 1

    logger.info(f"Thread pool completed. Processed {len(results)}/{len(app_list)} apps")
    return results


@flow(name="Process Category")
def process_category_flow(crawler: BaseCrawler, category: Dict[str, Any],
                         max_pages: int = -1, **kwargs) -> List[Dict[str, Any]]:
    """
    处理单个分类的子流程
    
    Args:
        crawler: 爬虫实例
        category: 分类信息
        max_pages: 最大页数限制
        **kwargs: 额外参数
        
    Returns:
        分类处理结果列表
    """
    logger = get_run_logger()
    category_name = category.get('name', category.get('tabName', 'unknown'))
    logger.info(f"Processing category: {category_name}")

    # 移除中间进度 artifacts，只保留最终结果
    import time
    import os

    start_time = time.time()

    # 创建空的进度跟踪函数（不创建 artifacts）
    def update_progress(current_page, total_pages, apps_processed, category_name):
        """空的进度跟踪函数 - 不创建 artifacts"""
        pass

    category_results = []
    page_num = 1
    total_apps_processed = 0
    pages_processed = 0
    
    while True:
        # 检查页数限制
        if max_pages > 0 and pages_processed >= max_pages:
            logger.info(f"Reached max pages limit ({max_pages}) for category {category_name}")
            break
        
        # 爬取列表页
        app_list, has_next_page = scrape_list_page_task(crawler, category, page_num)
        pages_processed += 1
        
        if not app_list:
            logger.info(f"No apps found for {category_name} on page {page_num}")
            break
        
        # 是否需要爬取详情页
        store_type = getattr(crawler, 'store_type', 'default')
        enable_detail_crawl = crawler.get_config_value('crawl_config.enable_detail_crawl', False)

        if enable_detail_crawl:
            # 检查是否使用容器内进程池模式
            use_internal_pool = crawler.get_config_value('crawl_config.use_internal_process_pool', False)

            if use_internal_pool:
                # 容器内线程池并发处理详情页
                logger.info(f"Using thread pool for {len(app_list)} apps")
                logger.info(f"Starting thread pool at {time.strftime('%H:%M:%S')}")
                logger.info(f"App list sample: {[app.get('name', 'unknown')[:20] for app in app_list[:3]]}")

                try:
                    logger.info("About to call process_details_with_thread_pool...")
                    results = process_details_with_thread_pool(app_list, crawler, **kwargs)
                    logger.info(f"Thread pool completed at {time.strftime('%H:%M:%S')}")
                    logger.info(f"Got {len(results)} results from thread pool")
                    category_results.extend(results)
                    total_apps_processed += len(results)

                    # 更新进度
                    logger.info("Updating progress after thread pool...")
                    update_progress(page_num, max_pages, total_apps_processed, category_name)
                    logger.info("Progress updated successfully")
                except Exception as e:
                    logger.error(f"Thread pool failed: {e}")
                    # 如果线程池失败，回退到单线程处理
                    logger.info("Falling back to single-threaded processing")
                    for app in app_list:
                        try:
                            app_id = app.get('detailId') or app.get('appId') or app.get('pkg_name') or app.get('store_id')
                            if app_id:
                                result = process_and_save_app_task(app, crawler, **kwargs)
                                if result:
                                    category_results.append(result)
                                    total_apps_processed += 1
                        except Exception as app_e:
                            logger.error(f"Failed to process app {app.get('name', 'unknown')}: {app_e}")

                    # 更新进度
                    update_progress(page_num, max_pages, total_apps_processed, category_name)
            else:
                # 原有的Prefect任务并发方式
                detail_futures = []
                for app in app_list:
                    # 支持不同商店的详情页ID字段
                    app_id = app.get('detailId') or app.get('appId') or app.get('pkg_name') or app.get('store_id')
                    if app_id:
                        future = scrape_detail_page_task.submit(crawler, str(app_id))
                        detail_futures.append((future, app))

                # 处理详情页结果
                for future, original_app in detail_futures:
                    detail_data = future.result()
                    if detail_data:
                        # 正确合并数据 - 将详情页数据合并到列表页数据中
                        # 智能合并数据：详情页数据优先，但不覆盖列表页的有效数据
                        merged_data = original_app.copy()

                        # 特别保存列表页的关键字段，避免被详情页的空值覆盖
                        list_page_download_url = original_app.get('download_url')

                        for key, value in detail_data.items():
                            # 只有当详情页数据非空时才覆盖列表页数据
                            if value is not None and value != '' and value != []:
                                merged_data[key] = value
                            # 如果列表页没有这个字段，则使用详情页数据（即使为空）
                            elif key not in merged_data:
                                merged_data[key] = value

                        # 特殊处理：如果详情页覆盖了列表页的有效download_url，恢复它
                        if (list_page_download_url and
                            list_page_download_url.startswith(('http://', 'https://')) and
                            (not merged_data.get('download_url') or merged_data.get('download_url') == '')):
                            merged_data['download_url'] = list_page_download_url
                            merged_data['_list_page_download_url'] = list_page_download_url  # 备份
                        result = process_and_save_app_task(merged_data, crawler, **kwargs)
                        if result:
                            category_results.append(result)
                            total_apps_processed += 1
                    else:
                        # 详情页失败，使用原始数据
                        result = process_and_save_app_task(original_app, crawler, **kwargs)
                        if result:
                            category_results.append(result)
                            total_apps_processed += 1

                # 更新进度
                update_progress(page_num, max_pages, total_apps_processed, category_name)
        else:
            # 未启用详情页爬取时的处理
            if enable_detail_crawl:
                # 其他商店使用批处理
                batch_size = BATCH_SIZE
                batch_delay = BATCH_DELAY
                for i in range(0, len(app_list), batch_size):
                    batch = app_list[i:i + batch_size]
                    logger.info(f"Processing batch {i//batch_size + 1}/{(len(app_list) + batch_size - 1)//batch_size} ({len(batch)} apps)")

                    # 批量处理详情页
                    detail_futures = scrape_detail_page_task.map([crawler] * len(batch),
                                                               [app.get('detailId') or app.get('appId') for app in batch])

                    # 等待详情页完成
                    detail_results = []
                    for future in as_completed(detail_futures):
                        detail_result = future.result()
                        detail_results.append(detail_result)

                    # 合并数据并保存
                    for j, detail_data in enumerate(detail_results):
                        if detail_data:
                            merged_data = {**batch[j], **detail_data}
                            result = process_and_save_app_task(merged_data, crawler, **kwargs)
                            if result:
                                category_results.append(result)
                                total_apps_processed += 1

                    # 批次间延迟
                    if i + batch_size < len(app_list):
                        time.sleep(batch_delay)

                # 更新进度
                update_progress(page_num, max_pages, total_apps_processed, category_name)
            else:
                # 未启用详情页爬取，直接处理列表页数据
                for app in app_list:
                    result = process_and_save_app_task(app, crawler, **kwargs)
                    if result:
                        category_results.append(result)
                        total_apps_processed += 1

                # 更新进度
                update_progress(page_num, max_pages, total_apps_processed, category_name)
        
        # 检查是否有下一页
        if not has_next_page:
            logger.info(f"No more pages for category {category_name}")
            break
        
        page_num += 1
        
        # 页面间延迟
        delay_range = crawler.get_config_value('crawl_config.page_delay_range', [1, 3])
        delay = random.uniform(delay_range[0], delay_range[1])
        time.sleep(delay)
    
    # 移除分类级别的 artifacts，只保留最终结果
    
    logger.info(f"Completed category {category_name}: {len(category_results)} apps processed")
    return category_results


@flow(name="Generic Crawler Flow")
def generic_crawler_flow(crawler_class, config_name: str, max_pages_per_category: int = -1, 
                        max_total_pages: int = -1, **kwargs) -> StandardCrawlReport:
    """
    通用爬虫主流程
    
    Args:
        crawler_class: 爬虫类
        config_name: 配置名称
        max_pages_per_category: 每个分类的最大页数
        max_total_pages: 总的最大页数
        **kwargs: 额外参数
        
    Returns:
        标准爬取报告
    """
    logger = get_run_logger()
    start_time = datetime.now()
    
    # 初始化爬虫
    crawler = crawler_class(config_name)
    store_type = crawler.store_type
    
    logger.info(f"Starting {store_type} crawler flow")
    logger.info(f"Max pages per category: {max_pages_per_category}")
    logger.info(f"Max total pages: {max_total_pages}")
    
    # 获取所有分类
    categories = crawler.get_categories()
    logger.info(f"Found {len(categories)} categories to process")
    
    all_results = []
    total_pages_processed = 0
    categories_processed = []
    
    for category in categories:
        category_name = category.get('name', category.get('tabName', 'unknown'))
        
        # 检查总页数限制
        if max_total_pages > 0 and total_pages_processed >= max_total_pages:
            logger.info(f"Reached total pages limit ({max_total_pages}). Stopping.")
            break
        
        # 计算当前分类可处理的页数
        remaining_pages = max_total_pages - total_pages_processed if max_total_pages > 0 else -1
        category_max_pages = max_pages_per_category
        
        if remaining_pages > 0 and (category_max_pages == -1 or remaining_pages < category_max_pages):
            category_max_pages = remaining_pages
        
        # 处理分类
        logger.info(f"Processing category: {category_name} (max pages: {category_max_pages})")
        category_results = process_category_flow(crawler, category, category_max_pages, **kwargs)
        
        if category_results:
            all_results.extend(category_results)
            categories_processed.append(category_name)
            
            # 估算处理的页数（粗略估计）
            apps_per_page = crawler.get_config_value('request_config.limit_per_page', 24)
            estimated_pages = len(category_results) // apps_per_page + 1
            total_pages_processed += estimated_pages
    
    end_time = datetime.now()
    
    # 创建最终报告
    extra_info = {
        'Categories Processed': len(categories_processed),
        'Total Categories Available': len(categories),
        'Estimated Pages Processed': total_pages_processed
    }
    
    report = create_standard_artifacts(
        all_results, 
        f"{store_type.title()} Crawler", 
        store_type,
        extra_info
    )
    
    # 更新报告时间信息
    report.start_time = start_time
    report.end_time = end_time
    report.categories_processed = categories_processed
    
    logger.info(f"Flow completed: {len(all_results)} total apps processed")
    logger.info(f"Duration: {report.duration_seconds:.1f} seconds")
    logger.info(f"Success rate: {report.success_rate:.1f}%")
    
    return report
