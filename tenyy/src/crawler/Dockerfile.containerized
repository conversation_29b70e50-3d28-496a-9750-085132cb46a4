# 容器化爬虫 Dockerfile
# 设计：一次完整爬取 = 一个容器 + 内部多进程

FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    git \
    netcat-openbsd \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目代码
COPY . .

# 设置Python路径
ENV PYTHONPATH=/app
ENV PATH=/usr/local/bin:$PATH

# 创建启动脚本
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
# 默认参数\n\
STORE_TYPE=${STORE_TYPE:-yinyongbao}\n\
APP_TYPE=${APP_TYPE:-app}\n\
MAX_PAGES=${MAX_PAGES:-1}\n\
MAX_CATEGORIES=${MAX_CATEGORIES:--1}\n\
\n\
echo "🚀 Starting containerized crawler..."\n\
echo "📦 Store: $STORE_TYPE"\n\
echo "📱 App type: $APP_TYPE"\n\
echo "📄 Max pages: $MAX_PAGES"\n\
echo "📂 Max categories: $MAX_CATEGORIES"\n\
echo "🔧 Process pool workers: $(nproc)"\n\
echo "🗄️  Database: ${POSTGRES_HOST:-app-db}:${POSTGRES_PORT:-5432}/${POSTGRES_DB:-tenyy_app}"\n\
\n\
# 等待数据库就绪\n\
echo "⏳ Waiting for database to be ready..."\n\
until nc -z ${POSTGRES_HOST:-app-db} ${POSTGRES_PORT:-5432}; do\n\
  echo "Database not ready, waiting..."\n\
  sleep 2\n\
done\n\
echo "✅ Database is ready!"\n\
\n\
# 设置环境变量\n\
export PYTHONPATH=/app\n\
\n\
# 运行容器化爬虫\n\
python tenyy/src/crawler/containerized_crawler.py \\\n\
  --store $STORE_TYPE \\\n\
  --app-type $APP_TYPE \\\n\
  --max-pages $MAX_PAGES \\\n\
  --max-categories $MAX_CATEGORIES\n\
' > /start-crawler.sh && chmod +x /start-crawler.sh

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD pgrep -f "containerized_crawler.py" || exit 1

# 启动爬虫
CMD ["/start-crawler.sh"]
