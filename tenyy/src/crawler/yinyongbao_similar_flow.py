#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
应用宝推荐应用爬虫Flow

基于现有的应用宝爬虫框架，实现推荐应用的爬取
"""

import sys
import argparse
from pathlib import Path
from typing import List, Dict, Any
from prefect import flow, task, get_run_logger
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from tenyy.src.crawler.yinyongbao_similar_crawler import YinyongbaoSimilarCrawler
from tenyy.src.common.data_writer import save_app_data


@task(name="Initialize Similar Crawler", retries=2)
def initialize_similar_crawler() -> YinyongbaoSimilarCrawler:
    """初始化推荐应用爬虫"""
    logger = get_run_logger()
    logger.info("Initializing Yinyongbao Similar Apps Crawler...")
    
    crawler = YinyongbaoSimilarCrawler()
    logger.info("✅ Yinyongbao Similar Apps Crawler initialized successfully")
    return crawler


@task(name="Get Package Categories", retries=2)
def get_package_categories(crawler: YinyongbaoSimilarCrawler, max_packages: int = None) -> List[Dict[str, Any]]:
    """获取包名分类列表"""
    logger = get_run_logger()
    logger.info("Getting package names from database...")

    # 直接调用爬虫的get_categories方法，传递限制参数
    categories = crawler.get_categories(max_categories=max_packages)

    logger.info(f"📦 Generated {len(categories)} package categories")
    return categories


@task(name="Process Similar Apps Category", retries=2)
def process_similar_apps_category(
    crawler: YinyongbaoSimilarCrawler, 
    category: Dict[str, Any]
) -> Dict[str, Any]:
    """处理单个包名的推荐应用"""
    logger = get_run_logger()
    package_name = category.get('package_name')
    
    logger.info(f"🔍 Processing similar apps for: {package_name}")
    
    try:
        # 爬取推荐应用列表
        app_list, _ = crawler.scrape_list_page(category, page_num=1)
        
        if not app_list:
            logger.warning(f"No similar apps found for {package_name}")
            return {
                'package_name': package_name,
                'success_count': 0,
                'failed_count': 0,
                'apps': []
            }
        
        logger.info(f"Found {len(app_list)} similar apps for {package_name}")
        
        # 处理每个推荐应用
        success_count = 0
        failed_count = 0
        processed_apps = []
        
        # 使用线程池并发处理详情页
        with ThreadPoolExecutor(max_workers=5) as executor:
            # 提交详情页任务
            future_to_app = {}
            for app in app_list:
                future = executor.submit(process_single_similar_app, crawler, app)
                future_to_app[future] = app
            
            # 收集结果
            for future in as_completed(future_to_app):
                app = future_to_app[future]
                try:
                    result = future.result()
                    if result and result.get('success'):
                        success_count += 1
                        processed_apps.append(result)
                    else:
                        failed_count += 1
                        logger.warning(f"Failed to process app: {app.get('pkgName', 'unknown')}")
                except Exception as e:
                    failed_count += 1
                    logger.error(f"Error processing app {app.get('pkgName', 'unknown')}: {e}")
        
        logger.info(f"✅ Processed {package_name}: {success_count} success, {failed_count} failed")
        
        return {
            'package_name': package_name,
            'success_count': success_count,
            'failed_count': failed_count,
            'apps': processed_apps
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to process similar apps for {package_name}: {e}")
        return {
            'package_name': package_name,
            'success_count': 0,
            'failed_count': 1,
            'apps': [],
            'error': str(e)
        }


def process_single_similar_app(crawler: YinyongbaoSimilarCrawler, app_data: Dict[str, Any]) -> Dict[str, Any]:
    """处理单个推荐应用"""
    # 在线程池中无法使用 get_run_logger()，使用爬虫的logger
    logger = crawler.logger
    pkg_name = app_data.get('pkgName')
    
    try:
        # 爬取详情页
        detail_data = crawler.scrape_detail_page(pkg_name)
        
        # 合并列表页和详情页数据
        if detail_data:
            merged_data = {**app_data, **detail_data}
        else:
            merged_data = app_data
        
        # 数据标准化
        standardized_data = crawler.process_and_standardize(merged_data)
        
        # 数据验证
        if not crawler.validate_data(standardized_data):
            logger.warning(f"Data validation failed for {pkg_name}")
            return {'success': False, 'pkg_name': pkg_name, 'error': 'validation_failed'}
        
        # 保存到数据库
        save_stats = save_app_data(standardized_data, store_type='yinyongbao')
        
        if save_stats is None:
            # 版本已存在
            return {
                'success': True,
                'pkg_name': pkg_name,
                'status': 'skipped',
                'reason': 'version_exists'
            }
        else:
            # 新数据保存成功
            return {
                'success': True,
                'pkg_name': pkg_name,
                'status': 'saved',
                'stats': save_stats
            }
            
    except Exception as e:
        logger.error(f"Failed to process similar app {pkg_name}: {e}")
        return {'success': False, 'pkg_name': pkg_name, 'error': str(e)}


@flow(name="Yinyongbao Similar Apps Crawler")
def yinyongbao_similar_crawler_flow(
    max_packages: int = 100,
    max_categories: int = 0
):
    """
    应用宝推荐应用爬虫主流程
    
    Args:
        max_packages: 最大处理包名数量
        max_categories: 最大处理分类数量（与max_packages相同作用）
    """
    logger = get_run_logger()
    logger.info("🚀 Starting Yinyongbao Similar Apps Crawler Flow")
    
    # 初始化爬虫
    crawler = initialize_similar_crawler()
    
    # 获取包名分类
    effective_max = max_categories if max_categories > 0 else max_packages
    categories = get_package_categories(crawler, max_packages=effective_max)
    
    if not categories:
        logger.error("❌ No package categories found")
        return
    
    logger.info(f"📊 Processing {len(categories)} package categories")
    
    # 处理每个包名的推荐应用
    total_success = 0
    total_failed = 0
    
    for i, category in enumerate(categories, 1):
        logger.info(f"📦 Processing category {i}/{len(categories)}: {category['package_name']}")
        
        result = process_similar_apps_category(crawler, category)
        
        total_success += result.get('success_count', 0)
        total_failed += result.get('failed_count', 0)
        
        if result.get('error'):
            logger.error(f"Category {category['package_name']} failed: {result['error']}")
    
    # 输出最终统计
    total_processed = total_success + total_failed
    success_rate = (total_success / total_processed * 100) if total_processed > 0 else 0
    
    logger.info("🎉 Yinyongbao Similar Apps Crawler completed!")
    logger.info(f"📊 Final Statistics:")
    logger.info(f"   - Categories processed: {len(categories)}")
    logger.info(f"   - Total apps processed: {total_processed}")
    logger.info(f"   - Successful: {total_success}")
    logger.info(f"   - Failed: {total_failed}")
    logger.info(f"   - Success rate: {success_rate:.1f}%")


def main():
    """命令行入口"""
    parser = argparse.ArgumentParser(description='Yinyongbao Similar Apps Crawler')
    parser.add_argument('--max-packages', type=int, default=100, 
                       help='Maximum number of packages to process (default: 100)')
    parser.add_argument('--max-categories', type=int, 
                       help='Maximum number of categories to process (alias for max-packages)')
    
    args = parser.parse_args()
    
    # 运行爬虫流程
    yinyongbao_similar_crawler_flow(
        max_packages=args.max_packages,
        max_categories=args.max_categories or 0
    )


if __name__ == "__main__":
    main()
