#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
YinyongbaoSimilarCrawler - 应用宝推荐应用爬虫

基于现有应用宝爬虫，爬取推荐应用数据
通过遍历数据库中的包名，获取每个应用的推荐应用列表
"""

import re
import json
from typing import List, Dict, Any, Optional, Tuple
from bs4 import BeautifulSoup

from .yinyongbao_crawler import YinyongbaoCrawler
from ..common.data_writer import get_db_session
from ..models.store_app import StoreApp


class YinyongbaoSimilarCrawler(YinyongbaoCrawler):
    """应用宝推荐应用爬虫"""
    
    def __init__(self, config_name: str = 'yinyongbao'):
        super().__init__(config_name)
        self.similar_base_url = "https://sj.qq.com/similar-app-list/"
        self.store_type = 'yinyongbao'
        
    def get_package_names_from_db(self, limit: Optional[int] = None, offset: int = 0) -> List[str]:
        """
        从数据库获取应用宝商店的包名列表

        Args:
            limit: 限制返回的包名数量，None表示不限制
            offset: 偏移量，用于分页获取

        Returns:
            包名列表
        """
        package_names = []

        try:
            with get_db_session() as session:
                query = session.query(StoreApp.app_id).filter(
                    StoreApp.store_type == self.store_type
                ).distinct().order_by(StoreApp.app_id)  # 添加排序确保结果一致

                if offset > 0:
                    query = query.offset(offset)

                if limit:
                    query = query.limit(limit)

                results = query.all()
                package_names = [result.app_id for result in results]

            self.logger.info(f"Retrieved {len(package_names)} package names from database (offset: {offset}, limit: {limit or 'unlimited'})")
            return package_names

        except Exception as e:
            self.logger.error(f"Failed to get package names from database: {e}")
            return []

    def get_total_package_count(self) -> int:
        """
        获取数据库中应用宝包名的总数量

        Returns:
            包名总数量
        """
        try:
            with get_db_session() as session:
                count = session.query(StoreApp.app_id).filter(
                    StoreApp.store_type == self.store_type
                ).distinct().count()

            self.logger.info(f"Total package count in database: {count}")
            return count

        except Exception as e:
            self.logger.error(f"Failed to get total package count: {e}")
            return 0
    
    def get_categories(self, max_categories: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        重写分类获取方法，返回包名列表作为"分类"
        每个包名被当作一个分类来处理

        Args:
            max_categories: 最大分类数量，None表示不限制

        Returns:
            包名列表，格式化为分类格式
        """
        # 根据max_categories参数决定是否限制数量
        package_names = self.get_package_names_from_db(limit=max_categories)

        # 将包名转换为分类格式
        categories = []
        for pkg_name in package_names:
            categories.append({
                'name': f'Similar to {pkg_name}',
                'package_name': pkg_name,
                'type': 'similar'
            })

        self.logger.info(f"Generated {len(categories)} similar app categories (limit: {max_categories or 'unlimited'})")
        return categories
    
    def scrape_list_page(self, category: Dict[str, Any], page_num: int = 1) -> Tuple[List[Dict[str, Any]], bool]:
        """
        重写列表页爬取方法，通过API获取推荐应用数据

        Args:
            category: 分类信息（包含package_name）
            page_num: 页码（推荐应用只有一页，忽略此参数）

        Returns:
            (应用列表, 是否有下一页)
        """
        package_name = category.get('package_name')
        if not package_name:
            self.logger.error("No package_name in category")
            return [], False

        # 推荐应用只有一页，page_num > 1 时直接返回空
        if page_num > 1:
            return [], False

        try:
            # 调用推荐应用API
            app_list = self._get_similar_apps_from_api(package_name)

            self.logger.info(f"Found {len(app_list)} similar apps for {package_name}")
            return app_list, False  # 推荐应用只有一页

        except Exception as e:
            self.logger.error(f"Failed to scrape similar apps for {package_name}: {e}")
            return [], False

    def _get_similar_apps_from_api(self, package_name: str) -> List[Dict[str, Any]]:
        """
        通过混合方式获取推荐应用数据：先从HTML页面获取推荐应用列表，再通过API获取完整信息

        Args:
            package_name: 源应用包名

        Returns:
            推荐应用列表
        """
        try:
            # 步骤1：从HTML页面获取推荐应用的包名列表
            similar_package_names = self._get_similar_package_names_from_html(package_name)

            if not similar_package_names:
                self.logger.warning(f"No similar apps found in HTML for {package_name}")
                return []

            self.logger.info(f"Found {len(similar_package_names)} similar apps from HTML")

            # 步骤2：通过常规API获取每个推荐应用的完整信息（包括下载链接）
            app_list = []
            for similar_pkg in similar_package_names:
                app_info = self._get_app_info_from_regular_api(similar_pkg, package_name)
                if app_info:
                    app_list.append(app_info)

            self.logger.info(f"Successfully got complete info for {len(app_list)} similar apps")
            return app_list

        except Exception as e:
            self.logger.error(f"Failed to get similar apps for {package_name}: {e}")
            return []

    def _get_similar_package_names_from_html(self, package_name: str) -> List[str]:
        """从HTML页面提取推荐应用的包名列表"""
        try:
            # 访问推荐应用页面
            similar_url = f"{self.similar_base_url}{package_name}"

            response = self.http_client.session.get(
                similar_url,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                },
                timeout=15
            )
            response.raise_for_status()

            # 从HTML中提取dynamicCardResponse
            html_content = response.text
            if '"dynamicCardResponse":' not in html_content:
                return []

            # 提取JSON数据
            start = html_content.find('"dynamicCardResponse":')
            start += len('"dynamicCardResponse":')

            # 简单的JSON提取
            brace_count = 0
            json_end = start

            for i, char in enumerate(html_content[start:], start):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        json_end = i + 1
                        break

            json_str = html_content[start:json_end]
            json_data = json.loads(json_str)

            # 提取包名列表
            package_names = []
            data = json_data.get('data', {})
            components = data.get('components', [])

            for component in components:
                comp_data = component.get('data', {})
                item_data = comp_data.get('itemData', [])

                for item in item_data:
                    pkg_name = item.get('pkg_name')
                    if pkg_name and pkg_name != package_name:  # 排除源应用本身
                        package_names.append(pkg_name)

            return package_names

        except Exception as e:
            self.logger.warning(f"Failed to extract package names from HTML: {e}")
            return []

    def _get_app_info_from_regular_api(self, pkg_name: str, source_package: str) -> Optional[Dict[str, Any]]:
        """通过常规应用宝API获取应用的完整信息（包括下载链接）"""
        try:
            # 尝试从应用和游戏两个API中查找
            for app_type in ['app', 'game']:
                payload = self._build_search_payload_for_package(pkg_name, app_type)

                response = self.http_client.post(self.api_url, json_data=payload)
                if not response:
                    continue

                json_response = response.json()
                app_info = self._extract_app_from_response(json_response, pkg_name, source_package)

                if app_info:
                    self.logger.debug(f"Found {pkg_name} in {app_type} API with download_url: {bool(app_info.get('download_url'))}")
                    return app_info

            self.logger.warning(f"Could not find complete info for {pkg_name}")
            return None

        except Exception as e:
            self.logger.warning(f"Failed to get app info for {pkg_name}: {e}")
            return None

    def _build_search_payload_for_package(self, pkg_name: str, app_type: str) -> Dict[str, Any]:
        """构建搜索特定包名的API请求"""
        layout = "YYB_HOME_APP_LIBRARY_LIST" if app_type == 'app' else "YYB_HOME_GAME_LIBRARY_LIST_ALGRITHM"
        scene = "app_center" if app_type == 'app' else "game_center"
        list_key = "cate_alias" if app_type == 'app' else "tag_alias"

        return {
            "head": {
                "cmd": "dynamicard_yybhome",
                "authInfo": {"businessId": "AuthName"},
                "deviceInfo": {"platform": 2},
                "userInfo": {"guid": "ca6544f9-b0b4-4539-972b-fa1b5c38c942"},
                "expSceneIds": "92170" if app_type == 'game' else "",
                "hostAppInfo": {"scene": scene}
            },
            "body": {
                "bid": "yybhome",
                "offset": 0,
                "size": 50,  # 增加返回数量以提高找到的概率
                "preview": False,
                "listS": {
                    "region": {"repStr": ["CN"]},
                    list_key: {"repStr": ["all"]}  # 搜索所有分类
                },
                "listI": {
                    "limit": {"repInt": [50]},
                    "offset": {"repInt": [0]}
                },
                "layout": layout
            }
        }

    def _extract_app_from_response(self, json_response: Dict[str, Any], target_package: str, source_package: str) -> Optional[Dict[str, Any]]:
        """从API响应中提取特定包名的应用信息"""
        try:
            data = json_response.get('data', {})
            components = data.get('components', [])

            for component in components:
                comp_data = component.get('data', {})

                # 尝试不同的数据结构
                for key in ['list', 'apps', 'items', 'data', 'itemData']:
                    if key in comp_data and isinstance(comp_data[key], list):
                        for item in comp_data[key]:
                            # 检查是否是目标应用
                            pkg_name = item.get('pkg_name') or item.get('pkgName') or item.get('appId')
                            if pkg_name == target_package:
                                # 找到了！构建完整的应用信息
                                app_info = item.copy()
                                app_info.update({
                                    'source_package': source_package,
                                    'is_similar_app': True,
                                    'pkgName': pkg_name,
                                    'appId': item.get('app_id', pkg_name),
                                    'appName': item.get('name'),
                                    'iconUrl': item.get('icon'),
                                    'categoryName': item.get('tags', ''),
                                    'score': float(item.get('average_rating', 0)) if item.get('average_rating') else None,
                                    'versionName': item.get('version_name'),
                                    'apkSize': int(item.get('apk_size', 0)) if item.get('apk_size') else None,
                                    'apkUrl': item.get('download_url'),  # 重要：这里应该有下载链接！
                                })

                                download_url = item.get('download_url')
                                if download_url and download_url.startswith(('http://', 'https://')):
                                    self.logger.info(f"Found download URL for {target_package}: {download_url[:100]}...")
                                    return app_info
                                else:
                                    self.logger.warning(f"Found {target_package} but no download URL")
                                    return app_info  # 即使没有下载链接也返回，后续可能会处理

            return None

        except Exception as e:
            self.logger.warning(f"Failed to extract app from response: {e}")
            return None

    def _parse_similar_apps_api_response(self, json_response: Dict[str, Any], source_package: str) -> List[Dict[str, Any]]:
        """
        解析推荐应用API响应

        Args:
            json_response: API响应数据
            source_package: 源应用包名

        Returns:
            应用列表
        """
        apps = []

        try:
            data = json_response.get('data', {})
            components = data.get('components', [])

            for component in components:
                # 查找推荐应用组件
                card_id = component.get('cardId', '')
                if 'similar' in card_id.lower() or 'game_list' in card_id:
                    comp_data = component.get('data', {})
                    item_data = comp_data.get('itemData', [])

                    for item in item_data:
                        # 跳过源应用本身
                        pkg_name = item.get('pkg_name')
                        if pkg_name == source_package:
                            continue

                        # 添加推荐应用标记
                        app_info = item.copy()
                        app_info.update({
                            'source_package': source_package,
                            'is_similar_app': True,
                            'pkgName': pkg_name,  # 添加标准字段名
                            'appId': item.get('app_id', pkg_name),
                            'appName': item.get('name'),
                            'iconUrl': item.get('icon'),
                            'categoryName': item.get('tags', ''),
                            'score': float(item.get('average_rating', 0)) if item.get('average_rating') else None,
                            'versionName': item.get('version_name'),
                            'apkSize': int(item.get('apk_size', 0)) if item.get('apk_size') else None,
                            'apkUrl': item.get('download_url'),  # 重要：这里有下载链接！
                        })

                        apps.append(app_info)

                        self.logger.debug(f"Found similar app: {pkg_name} with download_url: {item.get('download_url', 'None')}")

            self.logger.info(f"Parsed {len(apps)} similar apps from API response")
            return apps

        except Exception as e:
            self.logger.warning(f"Failed to parse similar apps API response: {e}")
            return []
    
    def _parse_similar_apps_html(self, html: str, source_package: str) -> List[Dict[str, Any]]:
        """
        解析推荐应用HTML页面
        
        Args:
            html: HTML内容
            source_package: 源应用包名
            
        Returns:
            应用列表
        """
        apps = []
        
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # 查找应用链接
            app_links = soup.find_all('a', href=re.compile(r'/appdetail/'))
            
            for link in app_links:
                try:
                    # 提取包名
                    href = link.get('href', '')
                    package_match = re.search(r'/appdetail/([^/?]+)', href)
                    if not package_match:
                        continue
                    
                    package_name = package_match.group(1)
                    
                    # 跳过源应用本身
                    if package_name == source_package:
                        continue
                    
                    # 提取应用信息
                    app_info = self._extract_app_info_from_link(link, package_name, source_package)
                    if app_info:
                        apps.append(app_info)
                        
                except Exception as e:
                    self.logger.warning(f"Failed to parse app link: {e}")
                    continue
            
        except Exception as e:
            self.logger.error(f"Failed to parse similar apps HTML: {e}")
        
        return apps
    
    def _extract_app_info_from_link(self, link_element, package_name: str, source_package: str) -> Optional[Dict[str, Any]]:
        """
        从链接元素中提取应用信息
        
        Args:
            link_element: BeautifulSoup链接元素
            package_name: 应用包名
            source_package: 源应用包名
            
        Returns:
            应用信息字典
        """
        try:
            app_info = {
                'pkgName': package_name,
                'appId': package_name,  # 使用包名作为appId
                'source_package': source_package,  # 记录推荐来源
                'is_similar_app': True,  # 标记为推荐应用
            }

            # 提取应用名称
            name_elem = link_element.find('img')
            if name_elem and name_elem.get('alt'):
                # 从alt属性提取应用名称，去掉年份和"官方新版"等后缀
                app_name = name_elem.get('alt', '').strip()
                app_name = re.sub(r'\d{4}官方新版$', '', app_name).strip()
                app_info['appName'] = app_name
                app_info['name'] = app_name  # 添加name字段，与标准化字段对应

            # 提取图标URL
            icon_elem = link_element.find('img')
            if icon_elem and icon_elem.get('src'):
                app_info['iconUrl'] = icon_elem.get('src')
                app_info['icon_url'] = icon_elem.get('src')  # 添加标准化字段名

            # 提取评分（如果有）
            rating_elem = link_element.find(text=re.compile(r'^\d+\.\d+$'))
            if rating_elem:
                try:
                    score = float(rating_elem.strip())
                    app_info['score'] = score
                    app_info['rating'] = score  # 添加标准化字段名
                except ValueError:
                    pass

            # 提取分类信息（如果有）
            category_elems = link_element.find_all(text=True)
            for text in category_elems:
                text = text.strip()
                if text and not text.replace('.', '').isdigit() and len(text) > 1 and text not in ['电脑版', '下载', '扫一扫下载']:
                    if 'categoryName' not in app_info:
                        app_info['categoryName'] = text
                        app_info['category'] = text  # 添加标准化字段名
                        break

            # 注意：推荐应用页面没有直接的下载链接，需要从详情页获取
            # 这里不设置download_url，让详情页来提供

            return app_info
            
        except Exception as e:
            self.logger.warning(f"Failed to extract app info for {package_name}: {e}")
            return None
    
    def get_download_url_from_api(self, package_name: str) -> Optional[str]:
        """
        通过应用宝API获取下载链接

        Args:
            package_name: 应用包名

        Returns:
            下载链接
        """
        try:
            # 尝试通过常规应用宝爬虫的列表页API来获取下载链接
            # 先尝试应用类型的API
            for app_type in ['app', 'game']:
                payload = self._build_search_payload(package_name, app_type)

                # 发送请求
                response = self.http_client.post(self.api_url, json_data=payload)

                if response:
                    json_response = response.json()
                    download_url = self._extract_download_url_from_response(json_response, package_name)
                    if download_url:
                        return download_url

            return None

        except Exception as e:
            self.logger.warning(f"Failed to get download URL for {package_name}: {e}")
            return None

    def _build_search_payload(self, package_name: str, app_type: str) -> Dict[str, Any]:
        """构建搜索特定包名的API请求"""
        layout = "YYB_HOME_APP_LIBRARY_LIST" if app_type == 'app' else "YYB_HOME_GAME_LIBRARY_LIST_ALGRITHM"
        scene = "app_center" if app_type == 'app' else "game_center"
        list_key = "cate_alias" if app_type == 'app' else "tag_alias"

        return {
            "head": {
                "cmd": "dynamicard_yybhome",
                "authInfo": {"businessId": "AuthName"},
                "deviceInfo": {"platform": 2},
                "userInfo": {"guid": "ca6544f9-b0b4-4539-972b-fa1b5c38c942"},
                "expSceneIds": "92170" if app_type == 'game' else "",
                "hostAppInfo": {"scene": scene}
            },
            "body": {
                "bid": "yybhome",
                "offset": 0,
                "size": 50,  # 增加返回数量以提高找到的概率
                "preview": False,
                "listS": {
                    "region": {"repStr": ["CN"]},
                    list_key: {"repStr": ["all"]}  # 搜索所有分类
                },
                "listI": {
                    "limit": {"repInt": [50]},
                    "offset": {"repInt": [0]}
                },
                "layout": layout
            }
        }

    def _extract_download_url_from_response(self, json_response: Dict[str, Any], target_package: str) -> Optional[str]:
        """从API响应中提取特定包名的下载链接"""
        try:
            data = json_response.get('data', {})
            components = data.get('components', [])

            for component in components:
                comp_data = component.get('data', {})

                # 尝试不同的数据结构
                for key in ['list', 'apps', 'items', 'data', 'itemData']:
                    if key in comp_data and isinstance(comp_data[key], list):
                        for item in comp_data[key]:
                            # 检查是否是目标应用
                            pkg_name = item.get('pkg_name') or item.get('pkgName') or item.get('appId')
                            if pkg_name == target_package:
                                download_url = item.get('download_url') or item.get('apkUrl')
                                if download_url and download_url.startswith(('http://', 'https://')):
                                    self.logger.info(f"Found download URL for {target_package}")
                                    return download_url

            return None

        except Exception as e:
            self.logger.warning(f"Failed to extract download URL from response: {e}")
            return None


        except Exception as e:
            self.logger.warning(f"Failed to get download URL for {package_name}: {e}")
            return None

    def process_and_standardize(self, raw_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        重写数据标准化方法，添加推荐应用特有的处理逻辑

        Args:
            raw_data: 原始数据
            **kwargs: 额外参数

        Returns:
            标准化数据
        """
        # 如果没有下载链接，尝试通过API获取
        if not raw_data.get('download_url') and raw_data.get('pkgName'):
            download_url = self.get_download_url_from_api(raw_data['pkgName'])
            if download_url:
                raw_data['download_url'] = download_url

        # 调用父类的标准化方法
        standardized_data = super().process_and_standardize(raw_data, **kwargs)

        # 添加推荐应用特有的元数据
        if raw_data.get('is_similar_app'):
            metadata = standardized_data.get('metadata', {})
            metadata.update({
                'is_similar_app': True,
                'source_package': raw_data.get('source_package'),
                'crawl_type': 'similar'
            })
            standardized_data['metadata'] = metadata

        return standardized_data
