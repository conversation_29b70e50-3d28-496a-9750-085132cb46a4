#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
容器化爬虫 - 一次完整爬取，一个容器，内部多进程

设计理念：
- 一次完整的爬取任务 = 一个容器
- 容器内部使用进程池并发处理详情页
- 避免频繁容器创建/销毁的开销
"""

import sys
import argparse
import time
import os
from pathlib import Path
from typing import Dict, Any, List
from prefect import flow

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from tenyy.src.crawler.framework.base_flow import process_category_flow
from tenyy.src.crawler.yinyongbao_crawler import YinyongbaoCrawler
from tenyy.src.crawler.huawei_crawler import HuaweiCrawler

# 尝试导入 Prefect artifacts，如果失败则使用空函数
try:
    from prefect.artifacts import create_markdown_artifact
except ImportError:
    def create_markdown_artifact(**kwargs):
        pass


@flow(name="Containerized Crawler")
def run_containerized_crawl(store_type: str, **kwargs):
    """
    运行容器化爬取
    
    Args:
        store_type: 商店类型 ('huawei' 或 'yinyongbao')
        **kwargs: 其他参数
    """
    print(f"🚀 Starting containerized crawl for {store_type}")
    print(f"📦 Container mode: One container with internal process pool")
    
    start_time = time.time()
    
    try:
        # 初始化爬虫
        if store_type == 'huawei':
            crawler = HuaweiCrawler()
            print("📱 Initialized Huawei crawler")
        elif store_type == 'yinyongbao':
            crawler = YinyongbaoCrawler()
            app_type = kwargs.get('app_type', 'app')
            crawler.set_app_type(app_type)
            print(f"📱 Initialized Yinyongbao crawler (type: {app_type})")
        else:
            raise ValueError(f"Unsupported store type: {store_type}")
        
        # 获取配置
        max_pages = kwargs.get('max_pages', 1)
        max_categories = kwargs.get('max_categories', -1)  # 默认无限制
        use_internal_pool = crawler.get_config_value('crawl_config.use_internal_process_pool', False)
        pool_workers = crawler.get_config_value('crawl_config.internal_pool_workers', 10)

        print(f"⚙️  Configuration:")
        print(f"   - Max pages per category: {max_pages}")
        print(f"   - Max categories: {max_categories}")
        print(f"   - Internal process pool: {use_internal_pool}")
        print(f"   - Pool workers: {pool_workers}")

        # 获取分类
        all_categories = crawler.get_categories()
        if store_type == 'yinyongbao':
            app_type = kwargs.get('app_type', 'app')
            all_categories = [cat for cat in all_categories if cat.get('app_type') == app_type]

        # 限制分类数量（避免华为134个分类全部处理）
        if max_categories > 0:
            categories = all_categories[:max_categories]
            print(f"📂 Limited to {len(categories)} categories (out of {len(all_categories)} total)")
        else:
            categories = all_categories
            print(f"📂 Processing all {len(categories)} categories")
        
        # 处理每个分类
        total_apps = 0
        total_success = 0
        total_categories = len(categories)

        # 创建总体进度跟踪（使用 markdown artifact，只有一个会被更新）
        overall_progress_key = f"crawl-status-{os.getpid()}"

        def update_overall_progress(completed_categories, current_category_name=""):
            progress_percentage = (completed_categories / total_categories * 100) if total_categories > 0 else 0

            # 创建进度条的 markdown
            progress_bar_length = 20
            filled_length = int(progress_bar_length * progress_percentage / 100)
            progress_bar = "█" * filled_length + "░" * (progress_bar_length - filled_length)

            markdown_content = f"""# 🚀 {store_type.upper()} 爬虫进度

## 📊 总体进度
**{progress_percentage:.1f}%** `{progress_bar}`

## 📋 详细状态
- **分类进度**: {completed_categories}/{total_categories} 已完成
- **当前分类**: {current_category_name or "准备中..."}
- **应用统计**: {total_success}/{total_apps} 成功

## ⏰ 实时状态
- **开始时间**: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}
- **运行时长**: {time.time() - start_time:.1f} 秒
"""

            create_markdown_artifact(
                key=overall_progress_key,
                markdown=markdown_content,
                description=f"{store_type} 爬虫实时进度"
            )

        # 初始进度
        update_overall_progress(0, "Starting...")

        # 保存所有结果用于最终报告
        all_category_results = []

        for i, category in enumerate(categories):
            category_name = category['name']
            print(f"\n📁 Processing category {i+1}/{len(categories)}: {category_name}")

            # 更新当前分类进度 - 开始处理
            update_overall_progress(i, f"Starting {category_name}")

            try:
                # 处理分类（使用容器内进程池）
                results = process_category_flow(
                    category=category,
                    crawler=crawler,
                    max_pages=max_pages,
                    is_game=(store_type == 'yinyongbao' and kwargs.get('app_type') == 'game')
                )

                # 保存结果用于最终报告
                all_category_results.extend(results)

                # 统计结果 - 包括 success 和 skipped 都算作成功处理
                category_success = len([r for r in results if r and r.get('status') in ['success', 'skipped']])
                total_apps += len(results)
                total_success += category_success

                # 更新完成后的进度
                update_overall_progress(i + 1, f"Completed {category_name}")

                print(f"✅ Category completed: {category_success}/{len(results)} apps successful")

            except Exception as e:
                print(f"❌ Category failed: {e}")
                # 即使失败也要更新进度
                update_overall_progress(i + 1, f"Failed {category_name}")
                continue

        # 最终进度更新
        duration = time.time() - start_time
        success_rate = (total_success / total_apps * 100) if total_apps > 0 else 0

        final_markdown = f"""# 🎉 {store_type.upper()} 爬虫完成！

## 📊 最终结果
**100.0%** `████████████████████`

## 📋 统计摘要
- **分类总数**: {total_categories} 个
- **应用总数**: {total_apps} 个
- **成功数量**: {total_success} 个
- **成功率**: {success_rate:.1f}%

## ⏰ 执行信息
- **开始时间**: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}
- **结束时间**: {time.strftime('%Y-%m-%d %H:%M:%S')}
- **总耗时**: {duration:.1f} 秒
- **平均速度**: {total_apps/duration:.2f} 应用/秒

## ✅ 任务状态
**已完成** - 所有分类处理完毕
"""

        create_markdown_artifact(
            key=overall_progress_key,
            markdown=final_markdown,
            description=f"{store_type} 爬虫最终结果"
        )

        # 创建标准的最终报告 artifact（作为独立 Flow）
        from tenyy.src.crawler.framework.flow_utils import create_standard_artifacts

        @flow(name="Create Final Artifacts")
        def create_final_artifacts_flow(results, flow_name, store_type, extra_info):
            """独立的 Flow 来创建最终 artifacts"""
            return create_standard_artifacts(results, flow_name, store_type, extra_info)

        # 创建标准报告
        if all_category_results:
            extra_info = {
                'Categories Processed': len(categories),
                'Total Categories Available': len(categories),
                'Container Mode': 'Internal Thread Pool'
            }

            # 作为独立 Flow 创建 artifacts
            create_final_artifacts_flow(
                all_category_results,
                f"{store_type.title()} Containerized Crawler",
                store_type,
                extra_info
            )
        
        # 计算总体统计
        duration = time.time() - start_time
        success_rate = (total_success / total_apps * 100) if total_apps > 0 else 0
        
        print(f"\n🎉 Containerized crawl completed!")
        print(f"📊 Results:")
        print(f"   - Total apps processed: {total_apps}")
        print(f"   - Successful: {total_success}")
        print(f"   - Success rate: {success_rate:.1f}%")
        print(f"   - Duration: {duration:.1f} seconds")
        print(f"   - Average: {duration/total_apps:.2f} seconds/app" if total_apps > 0 else "")
        
        return {
            'total_apps': total_apps,
            'successful': total_success,
            'success_rate': success_rate,
            'duration': duration
        }
        
    except Exception as e:
        print(f"❌ Containerized crawl failed: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='容器化爬虫 - 一次完整爬取')
    parser.add_argument('--store', choices=['huawei', 'yinyongbao'], required=True,
                       help='商店类型')
    parser.add_argument('--app-type', choices=['app', 'game'], default='app',
                       help='应用类型（仅用于应用宝）')
    parser.add_argument('--max-pages', type=int, default=1,
                       help='每个分类的最大页数')
    parser.add_argument('--max-categories', type=int, default=-1,
                       help='最大分类数量，-1表示无限制（华为有134个分类）')

    args = parser.parse_args()
    
    # 运行容器化爬取
    result = run_containerized_crawl(
        store_type=args.store,
        app_type=args.app_type,
        max_pages=args.max_pages,
        max_categories=args.max_categories
    )
    
    if result:
        print(f"\n✅ Crawl completed successfully")
        sys.exit(0)
    else:
        print(f"\n❌ Crawl failed")
        sys.exit(1)


if __name__ == "__main__":
    main()
