#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Huawei App Store Scraper - Prefect Flow (Modeled after yinyongbao_flow.py)

This script is a Prefect-based workflow to scrape applications from the Huawei App Store.
It uses the data saving and flow structure from the yinyongbao crawler for consistency,
but the network requests are specific to Huawei's API.
"""

import requests
import json
import time
import gzip
import brotli
import os
import argparse
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple

import pandas as pd
from prefect import flow, get_run_logger, task
from prefect.futures import as_completed
from prefect.artifacts import create_markdown_artifact, create_table_artifact

# Import the generic data writer, same as yinyongbao_flow.py
from tenyy.src.common.data_writer import save_app_data

# --- Constants & Configuration ---
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
HUAWEI_API_URL = "https://store-drcn.hispace.dbankcloud.cn/hwmarket/api/clientApi"
HUAWEI_HEADERS = {
    "accept": "application/json",
    "user-agent": "HiSpace##13.4.1.301##samsung##SM-S9210",
    "sysuseragent": "Mozilla/5.0 (Linux; Android 9; SM-S9210 Build/PQ3B.190801.********; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/92.0.4515.131 Mobile Safari/537.36",
    # 告诉服务器，我们发送的数据是 gzip 格式的。
    "content-encoding": "gzip",
    "content-type": "application/x-gzip",
    # 明确告诉服务器，我们只接受未经压缩的响应。
    "accept-encoding": "identity",
}

# --- Helper Functions (from pa_mobilehuawei.py) ---

def decompress_response(response: requests.Response) -> str:
    """
    Decompresses the response content based on 'content-encoding' header.
    Handles cases where content is not actually compressed despite headers.
    """
    logger = get_run_logger()
    encoding = response.headers.get('content-encoding', '').lower()
    content = response.content
    
    decoded_content = ""

    try:
        if encoding == 'br':
            try:
                decoded_content = brotli.decompress(content).decode('utf-8')
            except brotli.error:
                logger.warning(f"Brotli decompression failed. Assuming uncompressed content.")
                decoded_content = content.decode('utf-8', errors='ignore')
        elif encoding == 'gzip':
            try:
                decoded_content = gzip.decompress(content).decode('utf-8')
            except (gzip.BadGzipFile, OSError):
                logger.warning(f"Gzip decompression failed. Assuming uncompressed content.")
                decoded_content = content.decode('utf-8', errors='ignore')
        else:
            decoded_content = content.decode('utf-8', errors='ignore')
    except Exception as e:
        logger.error(f"An unexpected error occurred during decompression: {e}")
        # Fallback to decoding raw content
        decoded_content = content.decode('utf-8', errors='ignore')
        
    return decoded_content



def to_int(s):
    try:
        return int(s)
    except (ValueError, TypeError):
        return 0

def to_float(s):
    try:
        return float(s)
    except (ValueError, TypeError):
        return 0.0

def parse_download_count(count_str, unit_str):
    try:
        count = float(count_str)
        if '亿' in unit_str:
            return int(count * 100_000_000)
        if '万' in unit_str:
            return int(count * 10_000)
        return int(count)
    except (ValueError, TypeError):
        return 0

# --- Prefect Tasks ---

@task(name="Get Huawei Categories", retries=2, retry_delay_seconds=5)
def get_huawei_categories() -> List[Dict[str, Any]]:
    """Loads app and game categories from local JSON files."""
    logger = get_run_logger()
    logger.info("Loading Huawei app and game categories.")
    categories = []
    cat_files = {
        "app": os.path.join(BASE_DIR, "huaweiCat/huaweiapp.json"),
        "game": os.path.join(BASE_DIR, "huaweiCat/huaweigame.json")
    }
    for cat_type, file_path in cat_files.items():
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # The categories are nested within two levels of 'tabInfo'
                main_tabs = data.get('tabInfo', [])
                for main_tab in main_tabs:
                    sub_categories = main_tab.get('tabInfo', [])
                    for sub_cat in sub_categories:
                        sub_cat['type'] = cat_type  # Add the type ('app' or 'game')
                        categories.append(sub_cat)
        except FileNotFoundError:
            logger.error(f"Category file not found: {file_path}")
        except json.JSONDecodeError:
            logger.error(f"Error decoding JSON from file: {file_path}")
        except Exception as e:
            logger.error(f"An unexpected error occurred while processing {file_path}: {e}")
    return categories

@task(name="Scrape Huawei List Page", retries=3, retry_delay_seconds=10)
def scrape_huawei_list_page(category: Dict[str, Any], page_num: int) -> Tuple[List[Dict[str, str]], bool]:
    """Scrapes a list page for a given category to get app detail IDs and hasNextPage flag."""
    logger = get_run_logger()
    logger.info(f"Fetching list page for category '{category['tabName']}' (ID: {category['tabId']}), page {page_num}")
    
    timestamp = int(time.time() * 1000)
    uri = category['tabId']
    data = f"apsid=1740377506066&arkMaxVersion=0&arkMinVersion=0&arkSupport=0&brand=samsung&channelId=startFromLauncher&clientPackage=com.huawei.appmarket&cno=4010001&code=0200&contentPkg=&dataFilterSwitch=&deviceId=8bfe6b3ee196762d51cb5b99cd1968f254697fc7f4ac02cb252b954b856ad526&deviceIdRealType=3&deviceIdType=9&deviceSpecParams=%7B%22abis%22%3A%22x86_64%2Cx86%2Carm64-v8a%2Carmeabi-v7a%2Carmeabi%22%2C%22deviceFeatures%22%3A%22U%2C0g%2CP%2CB%2Ce%2Cp%2Ca%2Cb%2C04%2C0m%2Cm%2C08%2C03%2CC%2CS%2C0G%2Cq%2CL%2C2%2C6%2CY%2C0M%2Cf%2C1%2C8%2C9%2CO%2CH%2CW%2Cx%2CG%2Co%2C3%2CR%2Cd%2CQ%2Cn%2Cy%2CT%2Cr%2Cu%2C4%2CN%2CM%2C01%2C09%2CV%2C5%2Cc%2CF%2Ct%2C0L%2Ck%2C00%2Cw%2CE%2C02%2CI%2CJ%2Cj%2CD%2CX%2Ccom.google.android.feature.GOOGLE_BUILD%2Ccom.google.android.feature.GOOGLE_EXPERIENCE%2Ccom.google.android.feature.EXCHANGE_6_2%22%2C%22dpi%22%3A320%2C%22glVersion%22%3A%22OpenGL%20ES%203.1%22%2C%22openglExts%22%3A%22%22%2C%22preferLan%22%3A%22zh%22%2C%22usesLibrary%22%3A%225%2C6%2Cn%2CA%2C9%2C2%2Cb%2C1%2C7%2CF%2Cd%2CB%2CC%2Ccom.google.widevine.software.drm%22%7D&fid=0&globalTrace=null&gradeLevel=0&gradeType=&hardwareType=0&isSupportPage=1&manufacturer=samsung&maxResults=25&method=client.getTabDetail&net=1&oaidTrack=-2&osv=9&outside=0&recommendSwitch=1&reqPageNum={page_num}&roamingTime=0&runMode=2&serviceType=0&shellApkVer=0&sid=1740393706827&sign=h90010905i00000620000000000001000a0000000400100000010000000010000040230b0100011001000%404D19B1DFC50C4644B3E7BF9FE380B4DD&thirdPartyPkg=com.huawei.appmarket&translateFlag=1&ts={timestamp}&uri={uri}%3Faglocation%3D%257B%2522cres%2522%253A%257B%2522lPos%2522%253A2%252C%2522lid%2522%253A%2522914149%2522%252C%2522pos%2522%253A0%252C%2522resid%2522%253A%2522automore%257Cnormalcard%257C914149%257CA03306%2522%252C%2522rest%2522%253A%2522tab%2522%252C%2522tid%2522%253A%2522dist_a036239dc8c04d9985be428c4c0d5d73%2522%257D%252C%2522ftid%2522%253A%2522dist_4c2612bedc2042fe80b184cf22aa5333%2522%2C%2522pres%2522%253A%257B%2522lPos%2522%253A0%252C%2522pos%2522%253A6%252C%2522resid%2522%253A%2522a036239dc8c04d9985be428c4c0d5d73%2522%2C%2522rest%2522%253A%2522tab%2522%2C%2522tid%2522%253A%2522dist_3f8d5e0c095b40cb93c9809b8554856d%2522%257D%257D%26templateId%3D34850d944e844288a90ef5843d7d40ac%26requestId%3D8644e69213b04d7198a5f6db6aab8eba&ver=1.1"
    gzipped_data = gzip.compress(data.encode('utf-8'))

    try:
        response = requests.post(HUAWEI_API_URL, headers=HUAWEI_HEADERS, data=gzipped_data, timeout=20)
        response.raise_for_status()
        response_text = decompress_response(response)
        json_response = json.loads(response_text)



        has_next_page = bool(json_response.get('hasNextPage', 0))

        # New, targeted logic to find the main app list
        app_list_items = []
        if 'layoutData' in json_response:
            for layout in json_response.get('layoutData', []):
                # Heuristic: The main list's dataList contains items with a 'package' name.
                if layout.get('dataList') and isinstance(layout['dataList'], list):
                    if layout['dataList'] and 'package' in layout['dataList'][0]:
                        app_list_items = layout['dataList']
                        logger.info(f"Found app list in layout '{layout.get('layoutName')}' with {len(app_list_items)} items.")
                        break

        if not app_list_items:
            logger.warning(f"Could not find a valid app list for category '{category['tabName']}' page {page_num}. Stopping.")
            return [], False # Return False for has_next_page as we are stopping

        # 从列表页提取 detailId 和 name（如果存在）
        detail_ids = []
        for item in app_list_items:
            if 'detailId' in item:
                app_info = {'detailId': item['detailId']}
                # 如果列表页包含应用名称，也一并提取
                if 'name' in item:
                    app_info['name'] = item['name']
                detail_ids.append(app_info)

        logger.info(f"Extracted {len(detail_ids)} apps from list page, with names: {sum(1 for item in detail_ids if 'name' in item)}")
        return detail_ids, has_next_page

    except Exception as e:
        logger.error(f"Failed to fetch list page for category '{category['tabName']}': {e}")
        return [], False # Return False for has_next_page on error

@task(
    name="Scrape Huawei Detail Page",
    retries=3,
    retry_delay_seconds=[1, 5, 10],
    task_run_name="Scrape Detail - {detail_id:.70}"
)
def scrape_huawei_detail_page(detail_id: str) -> Optional[Dict[str, Any]]:
    """
    Fetches the detail page for a single app ID from Huawei's API.
    """
    logger = get_run_logger()
    log_detail_id = f"{detail_id[:70]}..." if len(detail_id) > 70 else detail_id
    logger.info(f"Fetching detail for app ID: {log_detail_id}")
    timestamp = int(time.time() * 1000)
    data = f"apsid=1740377506066&arkMaxVersion=0&arkMinVersion=0&arkSupport=0&brand=samsung&channelId=startFromLauncher&clientPackage=com.huawei.appmarket&cno=4010001&code=0200&contentPkg=&dataFilterSwitch=&deviceId=8bfe6b3ee196762d51cb5b99cd1968f254697fc7f4ac02cb252b954b856ad526&deviceIdRealType=3&deviceIdType=9&deviceSpecParams=%7B%22abis%22%3A%22x86_64%2Cx86%2Carm64-v8a%2Carmeabi-v7a%2Carmeabi%22%2C%22deviceFeatures%22%3A%22U%2C0g%2CP%2CB%2Ce%2Cp%2Ca%2Cb%2C04%2C0m%2Cm%2C08%2C03%2CC%2CS%2C0G%2Cq%2CL%2C2%2C6%2CY%2C0M%2Cf%2C1%2C8%2C9%2CO%2CH%2CW%2Cx%2CG%2Co%2C3%2CR%2Cd%2CQ%2Cn%2Cy%2CT%2Cr%2Cu%2C4%2CN%2CM%2C01%2C09%2CV%2C5%2Cc%2CF%2Ct%2C0L%2Ck%2C00%2Cw%2CE%2C02%2CI%2CJ%2Cj%2CD%2CX%2Ccom.google.android.feature.GOOGLE_BUILD%2Ccom.google.android.feature.GOOGLE_EXPERIENCE%2Ccom.google.android.feature.EXCHANGE_6_2%22%2C%22dpi%22%3A320%2C%22glVersion%22%3A%22OpenGL%20ES%203.1%22%2C%22openglExts%22%3A%22%22%2C%22preferLan%22%3A%22zh%22%2C%22usesLibrary%22%3A%225%2C6%2Cn%2CA%2C9%2C2%2Cb%2C1%2C7%2CF%2Cd%2CB%2CC%2Ccom.google.widevine.software.drm%22%7D&fid=0&globalTrace=null&gradeLevel=0&gradeType=&hardwareType=0&isSupportPage=1&manufacturer=samsung&maxResults=25&method=client.getTabDetail&net=1&oaidTrack=-2&osv=9&outside=0&recommendSwitch=1&reqPageNum=1&roamingTime=0&runMode=2&serviceType=0&shellApkVer=0&sid=1740462107265&sign=h90010905i00000620000000000001000a0000000400100000010000000010000040230b0100011001000%404D19B1DFC50C4644B3E7BF9FE380B4DD&thirdPartyPkg=com.huawei.appmarket&translateFlag=1&ts={timestamp}&uri={detail_id}&ver=1.1"
    gzipped_data = gzip.compress(data.encode('utf-8'))

    try:
        response = requests.post(HUAWEI_API_URL, headers=HUAWEI_HEADERS, data=gzipped_data, timeout=20)
        response.raise_for_status()
        response_text = decompress_response(response)
        json_response = json.loads(response_text)

        # Extract data from the complex JSON structure
        app_data, header_data = None, None
        if 'layoutData' in json_response:
            for layout in json_response['layoutData']:
                if layout.get('dataList') and isinstance(layout['dataList'], list) and len(layout['dataList']) > 0:
                    data_item = layout['dataList'][0]
                    if 'downurl' in data_item and app_data is None: app_data = data_item
                    if 'developer' in data_item and header_data is None: header_data = data_item
        
        if app_data and header_data:
            # Combine the two parts into a single dictionary
            return {**app_data, **header_data, 'big_detail': json_response}
        
        logger.warning(f"Could not parse detail data for app ID: {detail_id}")
        return None
    except Exception as e:
        logger.error(f"Failed to fetch detail for app ID {detail_id}: {e}")
        return None

@task(name="Process and Save App", retries=2, retry_delay_seconds=5)
def process_and_save_app(raw_data: Dict[str, Any], is_game: bool) -> Dict[str, Any]:
    """
    Processes a single app: standardizes fetched data and saves to DB.
    """
    logger = get_run_logger()
    detail_id = raw_data.get('detailId')
    detail_json_data = raw_data.get('big_detail', {})

    # --- 全新解析逻辑：遍历嵌套JSON提取数据 ---
    extracted_fields = {}
    layout_data = detail_json_data.get('layoutData', [])
    for card in layout_data:
        card_name = card.get('layoutName')
        data_list = card.get('dataList', [])
        if not data_list:
            continue
        item = data_list[0]

        if card_name == 'appdetailheadercard':
            extracted_fields['developer'] = item.get('developer')
            extracted_fields['category'] = item.get('tagName')
            extracted_fields['tags'] = item.get('tagName')
            extracted_fields['store_description_memo'] = item.get('memo')
            extracted_fields['labelNames'] = item.get('labelNames', [])
        elif card_name == 'appdetaildatacard':
            extracted_fields['rating'] = to_float(item.get('stars'))
            downloads_str = item.get('downloads', '0')
            unit = item.get('downloadUnit', '')
            extracted_fields['download_count'] = parse_download_count(downloads_str, unit)
            if item.get('gradeInfo'):
                extracted_fields['restrict_level'] = item.get('gradeInfo', {}).get('gradeDesc')
            extracted_fields['review_count'] = to_int(item.get('scoredBy'))
        elif card_name == 'detaileditorrecommendcard':
            extracted_fields['editor_intro'] = item.get('body')
        elif card_name == 'detailscreencardv4':
            extracted_fields['snap_shots'] = item.get('images', [])
        elif card_name == 'appdetailintrocardv2':
            extracted_fields['app_intro'] = item.get('appIntro')

    update_time_stamp = raw_data.get('updateTime')
    last_updated = datetime.fromtimestamp(int(update_time_stamp) / 1000) if update_time_stamp else datetime.now()

    # --- 基于全新解析结果，构建标准数据字典 ---
    standardized_data = {
        'pkg_name': raw_data.get('package'),
        'name': raw_data.get('name'),
        'store_id': detail_id,
        'developer': extracted_fields.get('developer'),
        'operator': raw_data.get('provider'),
        'category': extracted_fields.get('category'),
        'is_game': is_game,
        'icon': raw_data.get('icon'),
        'md5': raw_data.get('md5'),
        'version_name': raw_data.get('versionName'),
        'version_description': f"{extracted_fields.get('app_intro', '')}\n\n版本更新:\n{raw_data.get('newFeatures', '')}",
        'description': extracted_fields.get('store_description_memo'),
        'download_url': raw_data.get('downurl'),
        'file_size': to_int(raw_data.get('size')),
        'last_updated': last_updated,
        'rating': extracted_fields.get('rating'),
        'download_count': extracted_fields.get('download_count'),
        'review_count': extracted_fields.get('review_count'),
        'tags': extracted_fields.get('tags'),
        'privacy_agreement': raw_data.get('privacyUrl'),
        'permissions_list': raw_data.get('appPermission', []),
        'restrict_level': extracted_fields.get('restrict_level'),
        'editor_intro': extracted_fields.get('editor_intro'),
        'snap_shots': extracted_fields.get('snap_shots', []),
        'store_metadata': {
            'safe_labels': raw_data.get('safeLabels', []),
            'label_names': extracted_fields.get('labelNames', [])
        },
        'detail_json': detail_json_data
    }
    pkg_name = standardized_data['pkg_name']
    base_result = {
        'detail_id': detail_id, 
        'status': 'failure', 
        'error': 'Unknown', 
        'is_new_app': False, 
        'is_new_version': False,
        'pkg_name': pkg_name,
        'app_name': standardized_data['name']
    }

    try:

        if not pkg_name:
            base_result['error'] = 'Missing package name in scraped data'
            return base_result

        # 3. Save to database using the common data writer
        save_stats = save_app_data(standardized_data, store_type='huawei')

        if save_stats is None:  # Version already existed
            base_result.update({'status': 'skipped', 'error': 'Version already exists'})
        else:
            base_result.update({
                'status': 'success', 'error': None,
                'is_new_app': save_stats.get('new_app', False),
                'is_new_version': save_stats.get('new_version', False)
            })
        
        return base_result

    except Exception as e:
        logger.error(f"Failed to process app {detail_id}: {e}", exc_info=True)
        base_result['error'] = str(e)
        return base_result

@task(name="Create Category Artifacts")
def create_category_artifacts(results: List[Dict[str, Any]], category_name: str, category_id: str):
    """为单个分类创建临时的Markdown和表格报告。"""
    logger = get_run_logger()
    if not results:
        logger.info(f"Category '{category_name}': No results to create an artifact.")
        return

    df = pd.DataFrame([res for res in results if res])  # Filter out None results
    if df.empty:
        logger.info(f"Category '{category_name}': DataFrame is empty after filtering.")
        return
        
    total_processed = len(df)
    success_count = len(df[df['status'] == 'success'])
    skipped_count = len(df[df['status'] == 'skipped'])
    failure_count = len(df[df['status'] == 'failure'])
    new_apps = int(df['is_new_app'].sum())
    new_versions = int(df['is_new_version'].sum())

    # 1. Create Markdown Summary for the category
    markdown_summary = f"""### Category Summary: {category_name}

| 指标 | 数量 |
|:---|:---|
| **本分类处理应用** | {total_processed} |
| **成功** | {success_count} |
| **失败** | {failure_count} |
| **跳过(已存在)** | {skipped_count} |
| **新增应用** | {new_apps} |
| **新增版本** | {new_versions} |
"""
    create_markdown_artifact(
        key=f"category-{category_id}-summary",
        markdown=markdown_summary,
        description=f"爬虫运行的详细情况总结 - 分类: {category_name}。"
    )

    # 2. Create Failed Apps Table for the category
    failed_apps_df = df[df['status'] == 'failure']
    if not failed_apps_df.empty:
        create_table_artifact(
            key=f"category-{category_id}-failed-apps",
            table=failed_apps_df[['pkg_name', 'app_name', 'error']].to_dict(orient='records'),
            description=f"处理失败的应用列表及其错误信息 - 分类: {category_name}。"
        )

@task(name="Create Run Artifacts")
def create_run_artifacts(results: List[Dict[str, Any]], flow_name: str):
    """Generates Markdown and Table artifacts summarizing the flow run."""
    if not results:
        get_run_logger().warning("No results to process for artifacts.")
        return

    df = pd.DataFrame([r for r in results if r])
    if df.empty:
        get_run_logger().warning("DataFrame is empty, skipping artifact creation.")
        return

    total = len(df)
    success = len(df[df['status'] == 'success'])
    skipped = len(df[df['status'] == 'skipped'])
    failed = len(df[df['status'] == 'failure'])
    new_apps = int(df['is_new_app'].sum())
    new_versions = int(df['is_new_version'].sum())

    markdown = f"""# {flow_name} Report
| Metric | Count |
|:---|:---|
| **Total Processed** | {total} |
| **Success** | {success} |
| **Failed** | {failed} |
| **Skipped (Exists)** | {skipped} |
| **New Apps** | {new_apps} |
| **New Versions** | {new_versions} |
"""
    create_markdown_artifact(key="run-summary", markdown=markdown)

    if failed > 0:
        failed_df = df[df['status'] == 'failure']
        create_table_artifact(key="failed-apps", table=failed_df.to_dict(orient='records'))

@flow(
    name="Huawei App Store Crawler Flow",
    flow_run_name="Crawl Huawei - {max_pages} total pages - {time}"
)
def huawei_crawler_flow(max_pages: int = 10, time: str = None):
    """The main workflow to orchestrate the Huawei App Store scraping process."""
    logger = get_run_logger()
    logger.info(f"Starting Huawei scraping flow. Will check up to {max_pages} total pages across all categories.")
    
    all_results = []
    categories = get_huawei_categories()
    total_pages_crawled = 0
    
    for category in categories:
        if total_pages_crawled >= max_pages:
            logger.info(f"Total page limit of {max_pages} reached. Stopping flow.")
            break

        category_name = category['tabName']
        is_game = category['type'] == 'game'
        category_results = []
        logger.info(f"--- Starting Category: {category_name} ---")

        page_num = 1
        has_next_page_for_category = True
        while has_next_page_for_category and total_pages_crawled < max_pages:
            detail_id_list, has_next_page_for_category = scrape_huawei_list_page(category, page_num)
            total_pages_crawled += 1

            if not detail_id_list:
                logger.info(f"No apps found for '{category_name}' on page {page_num}. Moving to next category.")
                break  # Exit this category's while loop

            # 创建详情页任务，同时保存列表页信息
            detail_page_futures = []
            list_page_info = {}  # 保存列表页信息，key为detailId

            for d in detail_id_list:
                if 'detailId' in d:
                    detail_id = d['detailId']
                    list_page_info[detail_id] = d  # 保存整个列表页信息
                    detail_page_futures.append(scrape_huawei_detail_page.submit(detail_id))

            for future in as_completed(detail_page_futures):
                raw_data = future.result()
                if raw_data:
                    # 获取对应的列表页信息
                    detail_id = raw_data.get('detailId') or raw_data.get('store_id')
                    list_info = list_page_info.get(detail_id, {})

                    # 处理应用名称：优先使用列表页的名称，因为华为详情页的name字段通常是"应用介绍"等通用文本
                    if 'name' in list_info:
                        original_detail_name = raw_data.get('name')
                        raw_data['name'] = list_info['name']
                        logger.info(f"Using list page name for app {detail_id}: {list_info['name']} (detail page had: {original_detail_name})")
                    elif raw_data.get('name'):
                        logger.debug(f"No list page name available, using detail page name: {raw_data.get('name')}")
                    else:
                        logger.warning(f"No name available from either list or detail page for app {detail_id}")

                    result = process_and_save_app(raw_data, is_game)
                    category_results.append(result)
            
            if not has_next_page_for_category:
                logger.info(f"API indicates no more pages for '{category_name}' after page {page_num}. Moving to next category.")
            
            page_num += 1

        # Create intermediate artifact for the category
        if category_results:
            create_category_artifacts(category_results, category_name, category['tabId'])
            all_results.extend(category_results)

    logger.info(f"Flow finished. Processed {len(all_results)} total apps across all categories.")
    # Create final run artifacts with all results
    create_run_artifacts(all_results, "Huawei Crawler Overall")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run the Huawei App Store crawler flow.")
    parser.add_argument("--max-pages", type=int, default=10, help="Maximum number of pages to scrape per category.")
    args = parser.parse_args()

    # Run the flow with command-line arguments
    huawei_crawler_flow(max_pages=args.max_pages, time=datetime.now().strftime("%Y-%m-%d %H:%M"))
